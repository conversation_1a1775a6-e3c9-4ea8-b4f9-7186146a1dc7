name: Version Management

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Version increment type'
        required: true
        default: 'patch'
        type: choice
        options:
        - patch
        - minor
        - major
      release_notes:
        description: 'Release notes'
        required: false
        default: 'Automated version update'

jobs:
  version-update:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'

    - name: Read current version
      id: current_version
      run: |
        if [ -f "app/Views/dakoii/version.json" ]; then
          VERSION=$(php -r "
            \$version = json_decode(file_get_contents('app/Views/dakoii/version.json'), true);
            echo \$version['version'];
          ")
          echo "current_version=$VERSION" >> $GITHUB_OUTPUT
        else
          echo "current_version=1.0.0" >> $GITHUB_OUTPUT
        fi

    - name: Calculate new version
      id: new_version
      run: |
        CURRENT="${{ steps.current_version.outputs.current_version }}"
        TYPE="${{ github.event.inputs.version_type }}"
        
        IFS='.' read -r major minor patch <<< "$CURRENT"
        
        case $TYPE in
          major)
            major=$((major + 1))
            minor=0
            patch=0
            ;;
          minor)
            minor=$((minor + 1))
            patch=0
            ;;
          patch)
            patch=$((patch + 1))
            ;;
        esac
        
        NEW_VERSION="$major.$minor.$patch"
        echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
        echo "major=$major" >> $GITHUB_OUTPUT
        echo "minor=$minor" >> $GITHUB_OUTPUT
        echo "patch=$patch" >> $GITHUB_OUTPUT

    - name: Update version file
      run: |
        php -r "
        \$versionData = [
            'version' => '${{ steps.new_version.outputs.new_version }}',
            'major' => ${{ steps.new_version.outputs.major }},
            'minor' => ${{ steps.new_version.outputs.minor }},
            'patch' => ${{ steps.new_version.outputs.patch }},
            'release_date' => date('Y-m-d'),
            'release_notes' => '${{ github.event.inputs.release_notes }}',
            'build_number' => time(),
            'last_updated' => date('c')
        ];
        
        file_put_contents('app/Views/dakoii/version.json', json_encode(\$versionData, JSON_PRETTY_PRINT));
        "

    - name: Commit version update
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add app/Views/dakoii/version.json
        git commit -m "chore: bump version to ${{ steps.new_version.outputs.new_version }}"
        git push

    - name: Create Git Tag
      run: |
        git tag -a "v${{ steps.new_version.outputs.new_version }}" -m "Release v${{ steps.new_version.outputs.new_version }}"
        git push origin "v${{ steps.new_version.outputs.new_version }}"

    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ steps.new_version.outputs.new_version }}
        release_name: Release v${{ steps.new_version.outputs.new_version }}
        body: |
          ## Release Notes
          ${{ github.event.inputs.release_notes }}
          
          ## Changes
          - Version updated to ${{ steps.new_version.outputs.new_version }}
          - Build number: ${{ steps.new_version.outputs.build_number }}
          
          ## Installation
          Download the source code and follow the installation instructions in the README.
        draft: false
        prerelease: false

  auto-patch-version:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0

    - name: Check if version file exists
      id: check_version
      run: |
        if [ -f "app/Views/dakoii/version.json" ]; then
          echo "exists=true" >> $GITHUB_OUTPUT
        else
          echo "exists=false" >> $GITHUB_OUTPUT
        fi

    - name: Setup PHP
      if: steps.check_version.outputs.exists == 'true'
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'

    - name: Auto-increment patch version
      if: steps.check_version.outputs.exists == 'true'
      run: |
        php -r "
        \$version = json_decode(file_get_contents('app/Views/dakoii/version.json'), true);
        \$version['patch']++;
        \$version['version'] = \$version['major'] . '.' . \$version['minor'] . '.' . \$version['patch'];
        \$version['release_date'] = date('Y-m-d');
        \$version['release_notes'] = 'Automated patch update from commit';
        \$version['build_number'] = time();
        \$version['last_updated'] = date('c');
        
        file_put_contents('app/Views/dakoii/version.json', json_encode(\$version, JSON_PRETTY_PRINT));
        echo 'NEW_VERSION=' . \$version['version'] . PHP_EOL;
        " >> $GITHUB_ENV

    - name: Commit auto-patch update
      if: steps.check_version.outputs.exists == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add app/Views/dakoii/version.json
        git commit -m "chore: auto-increment patch version to $NEW_VERSION" || exit 0
        git push || exit 0
