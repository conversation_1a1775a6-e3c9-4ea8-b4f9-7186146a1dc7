name: Auto Version Management

on:
  push:
    branches: [ main, master ]
  release:
    types: [ published ]

jobs:
  auto-version:
    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'

    - name: Auto-generate version
      id: version
      run: |
        # Get commit count for build number
        BUILD_NUMBER=$(git rev-list --count HEAD)

        # Get recent commit messages
        COMMITS=$(git log --pretty=format:"%s" --since="1 week ago" | head -20)

        # Analyze commits for version increment
        VERSION_TYPE="patch"

        if echo "$COMMITS" | grep -qi "breaking:\|major:"; then
          VERSION_TYPE="major"
        elif echo "$COMMITS" | grep -qi "feat:\|feature:\|add:\|new:"; then
          VERSION_TYPE="minor"
        fi

        # Get current version or default
        if [ -f "app/Views/dakoii/version.json" ]; then
          CURRENT=$(php -r "
            \$v = json_decode(file_get_contents('app/Views/dakoii/version.json'), true);
            echo (\$v['major'] ?? 1) . '.' . (\$v['minor'] ?? 0) . '.' . (\$v['patch'] ?? 0);
          ")
        else
          CURRENT="1.0.0"
        fi

        # Calculate new version
        IFS='.' read -r major minor patch <<< "$CURRENT"

        case $VERSION_TYPE in
          major)
            major=$((major + 1))
            minor=0
            patch=0
            ;;
          minor)
            minor=$((minor + 1))
            patch=0
            ;;
          patch)
            patch=$((patch + 1))
            ;;
        esac

        NEW_VERSION="$major.$minor.$patch"

        # Generate release notes
        RELEASE_NOTES="Automated release based on recent commits"
        if echo "$COMMITS" | grep -qi "feat:\|feature:"; then
          FEATURES=$(echo "$COMMITS" | grep -i "feat:\|feature:" | head -3 | sed 's/^[^:]*: */- /')
          RELEASE_NOTES="New Features:\n$FEATURES"
        fi
        if echo "$COMMITS" | grep -qi "fix:\|bug:"; then
          FIXES=$(echo "$COMMITS" | grep -i "fix:\|bug:" | head -3 | sed 's/^[^:]*: */- /')
          RELEASE_NOTES="$RELEASE_NOTES\n\nBug Fixes:\n$FIXES"
        fi

        # Create version file
        php -r "
        \$versionData = [
            'version' => '$NEW_VERSION',
            'major' => $major,
            'minor' => $minor,
            'patch' => $patch,
            'release_date' => date('Y-m-d'),
            'release_notes' => '$RELEASE_NOTES',
            'build_number' => $BUILD_NUMBER,
            'last_updated' => date('c'),
            'git_commit' => '$(git rev-parse --short HEAD)'
        ];

        if (!is_dir('app/Views/dakoii')) {
            mkdir('app/Views/dakoii', 0755, true);
        }

        file_put_contents('app/Views/dakoii/version.json', json_encode(\$versionData, JSON_PRETTY_PRINT));
        "

        echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
        echo "version_type=$VERSION_TYPE" >> $GITHUB_OUTPUT

    - name: Check if version changed
      id: check_changes
      run: |
        if git diff --quiet app/Views/dakoii/version.json; then
          echo "changed=false" >> $GITHUB_OUTPUT
        else
          echo "changed=true" >> $GITHUB_OUTPUT
        fi

    - name: Commit version update
      if: steps.check_changes.outputs.changed == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Auto-Versioning"
        git add app/Views/dakoii/version.json
        git commit -m "chore: auto-update version to ${{ steps.version.outputs.new_version }} (${{ steps.version.outputs.version_type }})"
        git push

    - name: Create Git Tag
      if: steps.check_changes.outputs.changed == 'true'
      run: |
        git tag -a "v${{ steps.version.outputs.new_version }}" -m "Auto-release v${{ steps.version.outputs.new_version }}"
        git push origin "v${{ steps.version.outputs.new_version }}"

    - name: Create GitHub Release
      if: steps.check_changes.outputs.changed == 'true'
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ steps.version.outputs.new_version }}
        release_name: Auto-Release v${{ steps.version.outputs.new_version }}
        body: |
          🤖 **Automated Release**

          Version: **${{ steps.version.outputs.new_version }}**
          Type: **${{ steps.version.outputs.version_type }}** increment

          This release was automatically generated based on commit analysis.

          📝 **Recent Changes**
          See commit history for detailed changes.

          🔧 **Installation**
          Download the source code and follow standard installation procedures.
        draft: false
        prerelease: false
