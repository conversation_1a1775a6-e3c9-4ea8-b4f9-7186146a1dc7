<?php

if (!function_exists('get_app_version')) {
    /**
     * Get the current application version (auto-generated from git)
     *
     * @return array Version information
     */
    function get_app_version(): array
    {
        $versionFile = APPPATH . 'Views/dakoii/version.json';

        // Try to get version from git first
        $gitVersion = get_version_from_git();

        // If git version is available and different from stored version, update the file
        if ($gitVersion && (!file_exists($versionFile) || should_update_version($gitVersion, $versionFile))) {
            save_version_to_file($gitVersion, $versionFile);
            return $gitVersion;
        }

        // Fall back to stored version
        if (file_exists($versionFile)) {
            $content = file_get_contents($versionFile);
            if ($content !== false) {
                $version = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $version;
                }
            }
        }

        // Default version if nothing else works
        return [
            'version' => '1.0.0',
            'major' => 1,
            'minor' => 0,
            'patch' => 0,
            'release_date' => date('Y-m-d'),
            'release_notes' => 'Initial version',
            'build_number' => 1,
            'last_updated' => date('c')
        ];
    }
}

if (!function_exists('get_version_string')) {
    /**
     * Get the version string (e.g., "1.0.0")
     *
     * @return string Version string
     */
    function get_version_string(): string
    {
        $version = get_app_version();
        return $version['version'];
    }
}

if (!function_exists('get_version_from_git')) {
    /**
     * Automatically determine version from git commits
     *
     * @return array|null Version information or null if git not available
     */
    function get_version_from_git(): ?array
    {
        if (!is_dir('.git') && !file_exists('.git')) {
            return null;
        }

        try {
            // Get git commit count for build number
            $buildNumber = (int) shell_exec('git rev-list --count HEAD 2>/dev/null') ?: 1;

            // Get latest commit message
            $latestCommit = trim(shell_exec('git log -1 --pretty=format:"%s" 2>/dev/null') ?: '');

            // Get all commit messages since last version tag
            $commits = shell_exec('git log --pretty=format:"%s" --since="1 month ago" 2>/dev/null') ?: '';
            $commitLines = array_filter(explode("\n", $commits));

            // Analyze commits to determine version increment
            $versionIncrement = analyze_commits_for_version($commitLines);

            // Get base version (start from 1.0.0 if no previous version)
            $baseVersion = get_base_version();

            // Calculate new version
            $newVersion = calculate_new_version($baseVersion, $versionIncrement);

            // Generate release notes from recent commits
            $releaseNotes = generate_release_notes($commitLines);

            return [
                'version' => "{$newVersion['major']}.{$newVersion['minor']}.{$newVersion['patch']}",
                'major' => $newVersion['major'],
                'minor' => $newVersion['minor'],
                'patch' => $newVersion['patch'],
                'release_date' => date('Y-m-d'),
                'release_notes' => $releaseNotes,
                'build_number' => $buildNumber,
                'last_updated' => date('c'),
                'git_commit' => trim(shell_exec('git rev-parse --short HEAD 2>/dev/null') ?: 'unknown')
            ];

        } catch (Exception $e) {
            return null;
        }
    }
}

if (!function_exists('analyze_commits_for_version')) {
    /**
     * Analyze commit messages to determine version increment type
     *
     * @param array $commits Array of commit messages
     * @return string Version increment type: 'major', 'minor', or 'patch'
     */
    function analyze_commits_for_version(array $commits): string
    {
        $hasMajor = false;
        $hasMinor = false;

        foreach ($commits as $commit) {
            $commit = strtolower($commit);

            // Check for major version indicators
            if (strpos($commit, 'breaking:') !== false ||
                strpos($commit, 'major:') !== false ||
                strpos($commit, 'breaking change') !== false) {
                $hasMajor = true;
                break;
            }

            // Check for minor version indicators
            if (strpos($commit, 'feat:') !== false ||
                strpos($commit, 'feature:') !== false ||
                strpos($commit, 'add:') !== false ||
                strpos($commit, 'new:') !== false) {
                $hasMinor = true;
            }
        }

        if ($hasMajor) return 'major';
        if ($hasMinor) return 'minor';
        return 'patch';
    }
}

if (!function_exists('get_base_version')) {
    /**
     * Get base version from stored file or default
     *
     * @return array Base version numbers
     */
    function get_base_version(): array
    {
        $versionFile = APPPATH . 'Views/dakoii/version.json';

        if (file_exists($versionFile)) {
            $content = file_get_contents($versionFile);
            if ($content !== false) {
                $version = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return [
                        'major' => $version['major'] ?? 1,
                        'minor' => $version['minor'] ?? 0,
                        'patch' => $version['patch'] ?? 0
                    ];
                }
            }
        }

        return ['major' => 1, 'minor' => 0, 'patch' => 0];
    }
}

if (!function_exists('calculate_new_version')) {
    /**
     * Calculate new version based on increment type
     *
     * @param array $baseVersion Current version
     * @param string $incrementType Type of increment
     * @return array New version numbers
     */
    function calculate_new_version(array $baseVersion, string $incrementType): array
    {
        $major = $baseVersion['major'];
        $minor = $baseVersion['minor'];
        $patch = $baseVersion['patch'];

        switch ($incrementType) {
            case 'major':
                $major++;
                $minor = 0;
                $patch = 0;
                break;
            case 'minor':
                $minor++;
                $patch = 0;
                break;
            case 'patch':
            default:
                $patch++;
                break;
        }

        return ['major' => $major, 'minor' => $minor, 'patch' => $patch];
    }
}

if (!function_exists('generate_release_notes')) {
    /**
     * Generate release notes from commit messages
     *
     * @param array $commits Array of commit messages
     * @return string Generated release notes
     */
    function generate_release_notes(array $commits): string
    {
        if (empty($commits)) {
            return 'Automated release';
        }

        $features = [];
        $fixes = [];
        $others = [];

        foreach (array_slice($commits, 0, 10) as $commit) { // Limit to last 10 commits
            $commit = trim($commit);
            if (empty($commit)) continue;

            $lower = strtolower($commit);
            if (strpos($lower, 'feat:') !== false || strpos($lower, 'feature:') !== false) {
                $features[] = preg_replace('/^(feat|feature):\s*/i', '', $commit);
            } elseif (strpos($lower, 'fix:') !== false || strpos($lower, 'bug:') !== false) {
                $fixes[] = preg_replace('/^(fix|bug):\s*/i', '', $commit);
            } else {
                $others[] = $commit;
            }
        }

        $notes = [];
        if (!empty($features)) {
            $notes[] = "New Features:\n- " . implode("\n- ", $features);
        }
        if (!empty($fixes)) {
            $notes[] = "Bug Fixes:\n- " . implode("\n- ", $fixes);
        }
        if (!empty($others)) {
            $notes[] = "Other Changes:\n- " . implode("\n- ", array_slice($others, 0, 5));
        }

        return !empty($notes) ? implode("\n\n", $notes) : 'Automated release with latest changes';
    }
}

if (!function_exists('should_update_version')) {
    /**
     * Check if version should be updated
     *
     * @param array $newVersion New version data
     * @param string $versionFile Path to version file
     * @return bool True if should update
     */
    function should_update_version(array $newVersion, string $versionFile): bool
    {
        if (!file_exists($versionFile)) {
            return true;
        }

        $content = file_get_contents($versionFile);
        if ($content === false) {
            return true;
        }

        $currentVersion = json_decode($content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return true;
        }

        // Update if build number is different (new commits)
        return ($newVersion['build_number'] ?? 0) !== ($currentVersion['build_number'] ?? 0);
    }
}

if (!function_exists('save_version_to_file')) {
    /**
     * Save version data to file
     *
     * @param array $versionData Version data to save
     * @param string $versionFile Path to version file
     * @return bool True if successful
     */
    function save_version_to_file(array $versionData, string $versionFile): bool
    {
        $json = json_encode($versionData, JSON_PRETTY_PRINT);
        if ($json === false) {
            return false;
        }

        // Ensure directory exists
        $dir = dirname($versionFile);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        return file_put_contents($versionFile, $json) !== false;
    }
}

if (!function_exists('get_version_info')) {
    /**
     * Get formatted version information for display
     *
     * @return array Formatted version information
     */
    function get_version_info(): array
    {
        $version = get_app_version();

        return [
            'version' => $version['version'],
            'release_date' => $version['release_date'],
            'build_number' => $version['build_number'],
            'last_updated' => $version['last_updated'],
            'release_notes' => $version['release_notes'] ?? '',
            'git_commit' => $version['git_commit'] ?? ''
        ];
    }
}
