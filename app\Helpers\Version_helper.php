<?php

if (!function_exists('get_app_version')) {
    /**
     * Get the current application version
     * 
     * @return array|null Version information or null if file not found
     */
    function get_app_version(): ?array
    {
        $versionFile = APPPATH . 'Views/dakoii/version.json';
        
        if (!file_exists($versionFile)) {
            return null;
        }
        
        $content = file_get_contents($versionFile);
        if ($content === false) {
            return null;
        }
        
        $version = json_decode($content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return null;
        }
        
        return $version;
    }
}

if (!function_exists('get_version_string')) {
    /**
     * Get the version string (e.g., "1.0.0")
     * 
     * @return string Version string or "Unknown" if not available
     */
    function get_version_string(): string
    {
        $version = get_app_version();
        return $version['version'] ?? 'Unknown';
    }
}

if (!function_exists('update_app_version')) {
    /**
     * Update the application version
     * 
     * @param int $major Major version number
     * @param int $minor Minor version number  
     * @param int $patch Patch version number
     * @param string $releaseNotes Release notes for this version
     * @return bool True if successful, false otherwise
     */
    function update_app_version(int $major, int $minor, int $patch, string $releaseNotes = ''): bool
    {
        $versionFile = APPPATH . 'Views/dakoii/version.json';
        
        // Get current version to preserve build number
        $currentVersion = get_app_version();
        $buildNumber = isset($currentVersion['build_number']) ? $currentVersion['build_number'] + 1 : 1;
        
        $versionData = [
            'version' => "{$major}.{$minor}.{$patch}",
            'major' => $major,
            'minor' => $minor,
            'patch' => $patch,
            'release_date' => date('Y-m-d'),
            'release_notes' => $releaseNotes,
            'build_number' => $buildNumber,
            'last_updated' => date('c') // ISO 8601 format
        ];
        
        $json = json_encode($versionData, JSON_PRETTY_PRINT);
        if ($json === false) {
            return false;
        }
        
        return file_put_contents($versionFile, $json) !== false;
    }
}

if (!function_exists('increment_version')) {
    /**
     * Increment version based on type
     * 
     * @param string $type Type of increment: 'major', 'minor', or 'patch'
     * @param string $releaseNotes Release notes for this version
     * @return bool True if successful, false otherwise
     */
    function increment_version(string $type, string $releaseNotes = ''): bool
    {
        $version = get_app_version();
        if (!$version) {
            return false;
        }
        
        $major = $version['major'];
        $minor = $version['minor'];
        $patch = $version['patch'];
        
        switch (strtolower($type)) {
            case 'major':
                $major++;
                $minor = 0;
                $patch = 0;
                break;
            case 'minor':
                $minor++;
                $patch = 0;
                break;
            case 'patch':
            default:
                $patch++;
                break;
        }
        
        return update_app_version($major, $minor, $patch, $releaseNotes);
    }
}

if (!function_exists('get_version_info')) {
    /**
     * Get formatted version information for display
     * 
     * @return array Formatted version information
     */
    function get_version_info(): array
    {
        $version = get_app_version();
        if (!$version) {
            return [
                'version' => 'Unknown',
                'release_date' => 'Unknown',
                'build_number' => 0,
                'last_updated' => 'Unknown'
            ];
        }
        
        return [
            'version' => $version['version'],
            'release_date' => $version['release_date'] ?? 'Unknown',
            'build_number' => $version['build_number'] ?? 0,
            'last_updated' => $version['last_updated'] ?? 'Unknown',
            'release_notes' => $version['release_notes'] ?? ''
        ];
    }
}
