# Dependencies List for GoVPS Application

This document lists all dependencies used in the GoVPS application, which is built using the CodeIgniter 4 PHP framework.

## PHP Dependencies (from composer.json)

### Main Dependencies

| Package | Version | Description |
|---------|---------|-------------|
| php | ^8.1 | PHP programming language |
| ext-intl | * | PHP Internationalization extension |
| ext-json | * | PHP JSON extension |
| ext-mbstring | * | PHP Multibyte String extension |
| laminas/laminas-escaper | ^2.9 | Securely and safely escape HTML, HTML attributes, JavaScript, CSS, and URLs |
| psr/log | ^1.1 | Common interface for logging libraries |
| setasign/fpdi | ^2.6 | Collection of PHP classes facilitating developers to read pages from existing PDF documents |
| setasign/fpdi-tcpdf | ^2.3 | Integration package for FPDI and TCPDF |
| smalot/pdfparser | ^2.11 | PDF parser library that can read and extract information from PDF files |
| tecnickcom/tcpdf | ^6.8 | PHP class for generating PDF documents and barcodes |

### Development Dependencies

| Package | Version | Description |
|---------|---------|-------------|
| kint-php/kint | ^5.0.3 | Debugging tool for PHP developers |
| codeigniter/coding-standard | ^1.5 | Official Coding Standards for CodeIgniter based on PHP CS Fixer |
| fakerphp/faker | ^1.9 | PHP library that generates fake data for testing |
| friendsofphp/php-cs-fixer | 3.13.0 | A tool to automatically fix PHP Coding Standards issues |
| mikey179/vfsstream | ^1.6 | Virtual file system to mock the real file system in unit tests |
| nexusphp/cs-config | ^3.6 | PHP CS Fixer configuration for CodeIgniter |
| phpunit/phpunit | ^9.1 | Testing framework for PHP |
| predis/predis | ^1.1 \|\| ^2.0 | Flexible and feature-complete Redis client for PHP |

### Suggested Extensions

| Extension | Purpose |
|-----------|---------|
| ext-curl | For using CURLRequest class |
| ext-imagick | For using Image class ImageMagickHandler |
| ext-gd | For using Image class GDHandler |
| ext-exif | For running Image class tests |
| ext-simplexml | For formatting XML |
| ext-mysqli | For using MySQL |
| ext-oci8 | For using Oracle Database |
| ext-pgsql | For using PostgreSQL |
| ext-sqlsrv | For using SQL Server |
| ext-sqlite3 | For using SQLite3 |
| ext-memcache | For using Cache class MemcachedHandler with Memcache |
| ext-memcached | For using Cache class MemcachedHandler with Memcached |
| ext-redis | For using Cache class RedisHandler |
| ext-dom | For using TestResponse |
| ext-libxml | For using TestResponse |
| ext-xdebug | For using CIUnitTestCase::assertHeaderEmitted() |
| ext-fileinfo | Improves mime type detection for files |
| ext-readline | Improves CLI::input() usability |

## Transitive Dependencies (from composer.lock)

These are dependencies that are required by the direct dependencies:

| Package | Version | Required By |
|---------|---------|-------------|
| symfony/polyfill-mbstring | v1.31.0 | Required by smalot/pdfparser |
| ext-ctype | * | Required by laminas/laminas-escaper |
| ext-zlib | * | Required by setasign/fpdi and smalot/pdfparser |
| ext-iconv | * | Required by smalot/pdfparser |
| ext-curl | * | Required by tecnickcom/tcpdf |

## Framework

The application is built using CodeIgniter 4.6.1, which is a PHP MVC framework.

## No JavaScript Package Dependencies

No `package.json` file was found, indicating that the application does not use npm/yarn for JavaScript dependency management. Any JavaScript libraries used are likely included directly in the codebase or loaded from CDNs.

## No Python Dependencies

No `requirements.txt` file was found, indicating that the application does not use Python.
