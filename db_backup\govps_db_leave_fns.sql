-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 28, 2025 at 11:46 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `govps_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `adx_leave`
--

CREATE TABLE `adx_leave` (
  `id` int(11) NOT NULL,
  `code` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `adx_leave`
--

INSERT INTO `adx_leave` (`id`, `code`, `name`, `remarks`, `created_by`, `updated_by`, `created_at`, `updated_at`, `is_deleted`, `deleted_at`, `deleted_by`) VALUES
(1, 'CL', 'Compassionate Leave', '', 2, 2, '2025-05-28 01:08:13', '2025-05-28 01:16:07', 0, NULL, NULL),
(2, 'RL', 'Recreation Leave', '', 2, 2, '2025-05-28 01:16:20', '2025-05-28 01:18:13', 0, NULL, NULL),
(3, 'SL', 'Sick Leave', '', 2, NULL, '2025-05-28 01:16:33', '2025-05-28 01:16:33', 0, NULL, NULL),
(4, 'ML', 'Maternity Leave', '', 2, NULL, '2025-05-28 01:16:56', '2025-05-28 01:16:56', 0, NULL, NULL),
(5, 'PL', 'Paternity Leave', '', 2, NULL, '2025-05-28 01:17:10', '2025-05-28 01:17:10', 0, NULL, NULL),
(6, 'STL', 'Study Leave', '', 2, NULL, '2025-05-28 01:17:43', '2025-05-28 01:17:43', 0, NULL, NULL),
(7, 'FL', 'Furlough Leave', '', 2, NULL, '2025-05-28 01:18:35', '2025-05-28 01:18:35', 0, NULL, NULL),
(8, 'LWOP', 'Leave Without Pay', '', 2, NULL, '2025-05-28 01:19:33', '2025-05-28 01:19:33', 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `employee_leaves`
--

CREATE TABLE `employee_leaves` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `leave_type` varchar(50) NOT NULL,
  `date_from` date NOT NULL,
  `date_to` date NOT NULL,
  `is_paid` tinyint(4) NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `employee_leaves`
--

INSERT INTO `employee_leaves` (`id`, `org_id`, `employee_id`, `leave_type`, `date_from`, `date_to`, `is_paid`, `remarks`, `created_by`, `updated_by`, `created_at`, `updated_at`, `is_deleted`, `deleted_at`, `deleted_by`) VALUES
(1, 2, 290, 'RL', '2025-05-28', '2025-06-28', 0, 'This is the leave', NULL, NULL, '2025-05-28 00:27:14', '2025-05-28 01:27:43', 0, NULL, NULL),
(2, 2, 290, 'RL', '2025-03-11', '2025-06-12', 0, 'Leave without pay', NULL, NULL, '2025-05-28 01:27:28', '2025-05-28 01:27:28', 0, NULL, NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `adx_leave`
--
ALTER TABLE `adx_leave`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `employee_leaves`
--
ALTER TABLE `employee_leaves`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `adx_leave`
--
ALTER TABLE `adx_leave`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `employee_leaves`
--
ALTER TABLE `employee_leaves`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
