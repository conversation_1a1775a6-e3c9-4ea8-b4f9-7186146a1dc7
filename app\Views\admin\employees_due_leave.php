<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- Employees Due for Leave Content -->
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="h4 fw-bold text-gray-800 mb-1">Employees Due for Recreation Leave</h2>
            <p class="text-muted mb-0">Employees who are eligible for Recreation Leave (RL) in <?= $current_year ?></p>
        </div>
        <div>
            <a href="<?= base_url('dashboard') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Summary Card -->
    <div class="card mb-4 border-start border-warning border-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="fw-bold text-gray-800 mb-1">
                        <i class="fas fa-calendar-times text-warning me-2"></i>
                        Total Employees Due for Leave: <?= count($employees_due) ?>
                    </h5>
                    <p class="text-muted mb-0">
                        Recreation Leave is due every 2 years from commencement date or last leave taken.
                    </p>
                </div>
                <div class="col-auto">
                    <span class="text-muted"><?= date('l, F j, Y') ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees Due for Leave Table -->
    <?php if (count($employees_due) > 0): ?>
    <div class="card">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Employees Due for Recreation Leave</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Employee</th>
                            <th>File No.</th>
                            <th>Commencement Date</th>
                            <th>Last Leave Date</th>
                            <th>Due Year</th>
                            <th>Years Overdue</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($employees_due as $employee): ?>
                        <tr>
                            <td class="px-4">
                                <div class="d-flex align-items-center">
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                        <?php if ($employee['id_photo']): ?>
                                            <img src="<?= base_url($employee['id_photo']) ?>" alt="Profile" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                        <?php else: ?>
                                            <i class="fas fa-user-tie text-muted"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <div class="fw-medium"><?= esc($employee['fname'] . ' ' . $employee['lname']) ?></div>
                                        <small class="text-muted"><?= esc($employee['primary_email']) ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info bg-opacity-10 text-info">
                                    <?= esc($employee['fileno']) ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($employee['commence_date']): ?>
                                    <?= date('M d, Y', strtotime($employee['commence_date'])) ?>
                                <?php else: ?>
                                    <span class="text-muted">Not set</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($employee['last_leave_date']): ?>
                                    <?= date('M d, Y', strtotime($employee['last_leave_date'])) ?>
                                <?php else: ?>
                                    <span class="text-muted">No previous leave</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-warning bg-opacity-10 text-warning">
                                    <?= $employee['due_year'] ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($employee['years_overdue'] > 0): ?>
                                    <span class="badge bg-danger bg-opacity-10 text-danger">
                                        <?= $employee['years_overdue'] ?> year<?= $employee['years_overdue'] > 1 ? 's' : '' ?> overdue
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-success bg-opacity-10 text-success">
                                        Due this year
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge <?= $employee['status'] == 'active' ? 'bg-success' : 'bg-danger' ?> bg-opacity-10 <?= $employee['status'] == 'active' ? 'text-success' : 'text-danger' ?>">
                                    <?= ucfirst($employee['status']) ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- No Employees Due -->
    <div class="card">
        <div class="card-body text-center py-5">
            <div class="mb-4">
                <i class="fas fa-calendar-check text-success" style="font-size: 4rem;"></i>
            </div>
            <h4 class="text-muted mb-3">No Employees Due for Leave</h4>
            <p class="text-muted mb-0">
                All employees are up to date with their Recreation Leave schedule.
            </p>
        </div>
    </div>
    <?php endif; ?>

    <!-- Information Card -->
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h6 class="card-title mb-0">
                <i class="fas fa-info-circle text-info me-2"></i>
                Leave Calculation Information
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold">For employees without previous RL leave:</h6>
                    <p class="text-muted mb-3">
                        Leave is calculated as 2 years from the employee's commencement date.
                    </p>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold">For employees with previous RL leave:</h6>
                    <p class="text-muted mb-3">
                        Leave is calculated as 2 years from the end date of their last Recreation Leave.
                    </p>
                </div>
            </div>
            <div class="alert alert-info mb-0">
                <i class="fas fa-lightbulb me-2"></i>
                <strong>Note:</strong> Only Recreation Leave (RL) records are considered for this calculation.
                Other leave types do not affect the Recreation Leave schedule.
            </div>
        </div>
    </div>

    <!-- Navigation Links -->
    <div class="card mt-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <a href="<?= base_url('employees_due_leave_next_year') ?>" class="btn btn-info w-100">
                        <i class="fas fa-calendar-plus me-2"></i>
                        View Employees Due for Leave (<?= $current_year + 1 ?>)
                    </a>
                </div>
                <div class="col-md-6">
                    <a href="<?= base_url('dashboard') ?>" class="btn btn-primary w-100">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
