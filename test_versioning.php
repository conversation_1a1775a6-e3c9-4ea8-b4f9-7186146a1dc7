<?php
/**
 * Simple test script to verify the automated versioning system
 * Run this from the project root: php test_versioning.php
 */

// Include CodeIgniter bootstrap
require_once 'app/Config/Paths.php';
$paths = new Config\Paths();
require_once $paths->systemDirectory . '/bootstrap.php';

// Load the version helper
require_once 'app/Helpers/Version_helper.php';

echo "🧪 Testing Automated Versioning System\n";
echo "=====================================\n\n";

// Test 1: Get current version
echo "1. Testing get_app_version():\n";
$version = get_app_version();
print_r($version);
echo "\n";

// Test 2: Get version string
echo "2. Testing get_version_string():\n";
$versionString = get_version_string();
echo "Version String: $versionString\n\n";

// Test 3: Get version info
echo "3. Testing get_version_info():\n";
$versionInfo = get_version_info();
print_r($versionInfo);
echo "\n";

// Test 4: Test commit analysis
echo "4. Testing commit analysis:\n";
$testCommits = [
    "fix: resolve login issue",
    "feat: add new dashboard",
    "docs: update README",
    "breaking: change API structure"
];

foreach ($testCommits as $commit) {
    $type = analyze_commits_for_version([$commit]);
    echo "Commit: '$commit' → Version Type: $type\n";
}
echo "\n";

// Test 5: Test version calculation
echo "5. Testing version calculation:\n";
$baseVersion = ['major' => 1, 'minor' => 2, 'patch' => 3];
$increments = ['patch', 'minor', 'major'];

foreach ($increments as $increment) {
    $newVersion = calculate_new_version($baseVersion, $increment);
    echo "Base: 1.2.3 + $increment → {$newVersion['major']}.{$newVersion['minor']}.{$newVersion['patch']}\n";
}
echo "\n";

// Test 6: Test release notes generation
echo "6. Testing release notes generation:\n";
$testCommits = [
    "feat: add user dashboard",
    "fix: resolve login timeout",
    "docs: update installation guide",
    "feat: implement search feature"
];
$releaseNotes = generate_release_notes($testCommits);
echo "Generated Release Notes:\n";
echo $releaseNotes . "\n\n";

// Test 7: Check if git is available
echo "7. Checking git availability:\n";
if (is_dir('.git') || file_exists('.git')) {
    echo "✅ Git repository detected\n";
    
    // Try to get git info
    $gitCommit = trim(shell_exec('git rev-parse --short HEAD 2>/dev/null') ?: 'unknown');
    $gitCount = (int) shell_exec('git rev-list --count HEAD 2>/dev/null') ?: 0;
    
    echo "Git Commit: $gitCommit\n";
    echo "Git Count: $gitCount\n";
} else {
    echo "⚠️  No git repository found\n";
}
echo "\n";

// Test 8: Check version file
echo "8. Checking version file:\n";
$versionFile = 'app/Views/dakoii/version.json';
if (file_exists($versionFile)) {
    echo "✅ Version file exists: $versionFile\n";
    $fileContent = file_get_contents($versionFile);
    $fileData = json_decode($fileContent, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ Version file is valid JSON\n";
        echo "File Version: {$fileData['version']}\n";
    } else {
        echo "❌ Version file contains invalid JSON\n";
    }
} else {
    echo "⚠️  Version file not found, will be created automatically\n";
}
echo "\n";

echo "🎉 Automated Versioning System Test Complete!\n";
echo "\nTo test the full system:\n";
echo "1. Make a commit with 'feat: test new feature'\n";
echo "2. Push to main branch\n";
echo "3. Check GitHub Actions for automatic version update\n";
echo "4. Verify new version appears in the application\n";
?>
