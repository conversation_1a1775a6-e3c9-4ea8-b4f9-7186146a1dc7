<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class VersionCommand extends BaseCommand
{
    protected $group       = 'App';
    protected $name        = 'version';
    protected $description = 'Manage application version';
    protected $usage       = 'version [action] [options]';
    protected $arguments   = [
        'action' => 'Action to perform: show, increment, set'
    ];
    protected $options     = [
        '--type'  => 'Version increment type: major, minor, patch (default: patch)',
        '--notes' => 'Release notes for the version',
        '--major' => 'Major version number (for set action)',
        '--minor' => 'Minor version number (for set action)',
        '--patch' => 'Patch version number (for set action)',
    ];

    public function run(array $params)
    {
        helper('version');
        
        $action = $params[0] ?? 'show';
        
        switch ($action) {
            case 'show':
                $this->showVersion();
                break;
            case 'increment':
                $this->incrementVersion($params);
                break;
            case 'set':
                $this->setVersion($params);
                break;
            default:
                CLI::error("Unknown action: {$action}");
                CLI::write("Available actions: show, increment, set");
                break;
        }
    }

    private function showVersion()
    {
        $version = get_app_version();
        
        if (!$version) {
            CLI::error('Version file not found or invalid');
            return;
        }

        CLI::write('Current Application Version:', 'green');
        CLI::write('');
        CLI::write("Version: {$version['version']}", 'yellow');
        CLI::write("Major: {$version['major']}");
        CLI::write("Minor: {$version['minor']}");
        CLI::write("Patch: {$version['patch']}");
        CLI::write("Release Date: {$version['release_date']}");
        CLI::write("Build Number: {$version['build_number']}");
        CLI::write("Last Updated: {$version['last_updated']}");
        
        if (!empty($version['release_notes'])) {
            CLI::write('');
            CLI::write('Release Notes:', 'cyan');
            CLI::write($version['release_notes']);
        }
    }

    private function incrementVersion(array $params)
    {
        $type = CLI::getOption('type') ?? 'patch';
        $notes = CLI::getOption('notes') ?? '';
        
        if (!in_array($type, ['major', 'minor', 'patch'])) {
            CLI::error("Invalid increment type: {$type}");
            CLI::write("Valid types: major, minor, patch");
            return;
        }

        $currentVersion = get_app_version();
        if (!$currentVersion) {
            CLI::error('Version file not found or invalid');
            return;
        }

        CLI::write("Current version: {$currentVersion['version']}", 'yellow');
        
        if (increment_version($type, $notes)) {
            $newVersion = get_version_string();
            CLI::write("Version successfully incremented to: {$newVersion}", 'green');
            
            // Show what changed
            $this->showVersionChange($currentVersion, get_app_version(), $type);
        } else {
            CLI::error('Failed to increment version');
        }
    }

    private function setVersion(array $params)
    {
        $major = CLI::getOption('major');
        $minor = CLI::getOption('minor');
        $patch = CLI::getOption('patch');
        $notes = CLI::getOption('notes') ?? '';

        if ($major === null || $minor === null || $patch === null) {
            CLI::error('All version numbers (major, minor, patch) are required for set action');
            CLI::write('Example: php spark version set --major=2 --minor=0 --patch=0 --notes="Major release"');
            return;
        }

        $major = (int) $major;
        $minor = (int) $minor;
        $patch = (int) $patch;

        if ($major < 0 || $minor < 0 || $patch < 0) {
            CLI::error('Version numbers must be non-negative integers');
            return;
        }

        $currentVersion = get_app_version();
        if ($currentVersion) {
            CLI::write("Current version: {$currentVersion['version']}", 'yellow');
        }

        if (update_app_version($major, $minor, $patch, $notes)) {
            $newVersion = "{$major}.{$minor}.{$patch}";
            CLI::write("Version successfully set to: {$newVersion}", 'green');
            
            if ($currentVersion) {
                $this->showVersionChange($currentVersion, get_app_version(), 'manual');
            }
        } else {
            CLI::error('Failed to set version');
        }
    }

    private function showVersionChange(array $oldVersion, array $newVersion, string $type)
    {
        CLI::write('');
        CLI::write('Version Change Summary:', 'cyan');
        CLI::write("Type: {$type}");
        CLI::write("From: {$oldVersion['version']} → To: {$newVersion['version']}");
        
        if ($oldVersion['major'] !== $newVersion['major']) {
            CLI::write("Major: {$oldVersion['major']} → {$newVersion['major']}", 'red');
        }
        if ($oldVersion['minor'] !== $newVersion['minor']) {
            CLI::write("Minor: {$oldVersion['minor']} → {$newVersion['minor']}", 'yellow');
        }
        if ($oldVersion['patch'] !== $newVersion['patch']) {
            CLI::write("Patch: {$oldVersion['patch']} → {$newVersion['patch']}", 'green');
        }
        
        CLI::write("Build: {$oldVersion['build_number']} → {$newVersion['build_number']}");
        
        if (!empty($newVersion['release_notes'])) {
            CLI::write('');
            CLI::write('Release Notes:', 'cyan');
            CLI::write($newVersion['release_notes']);
        }
    }
}
