<?php
/**
 * Simple version checker - no CodeIgniter dependencies
 */

echo "🔍 Checking Current Application Version\n";
echo "=====================================\n\n";

// Check version file
$versionFile = 'app/Views/dakoii/version.json';

if (file_exists($versionFile)) {
    $content = file_get_contents($versionFile);
    $version = json_decode($content, true);
    
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "📋 Current Version Information:\n";
        echo "Version: " . $version['version'] . "\n";
        echo "Major: " . $version['major'] . "\n";
        echo "Minor: " . $version['minor'] . "\n";
        echo "Patch: " . $version['patch'] . "\n";
        echo "Release Date: " . $version['release_date'] . "\n";
        echo "Build Number: " . $version['build_number'] . "\n";
        echo "Git Commit: " . ($version['git_commit'] ?? 'unknown') . "\n";
        echo "Last Updated: " . $version['last_updated'] . "\n\n";
        
        if (!empty($version['release_notes'])) {
            echo "📝 Release Notes:\n";
            echo $version['release_notes'] . "\n\n";
        }
    } else {
        echo "❌ Version file contains invalid JSON\n";
    }
} else {
    echo "⚠️  Version file not found at: $versionFile\n";
    echo "The version will be auto-created when the application runs.\n";
}

// Check git information if available
echo "🔧 Git Information:\n";
if (is_dir('.git') || file_exists('.git')) {
    echo "✅ Git repository detected\n";
    
    $gitCommit = trim(shell_exec('git rev-parse --short HEAD 2>/dev/null') ?: 'unknown');
    $gitCount = (int) shell_exec('git rev-list --count HEAD 2>/dev/null') ?: 0;
    $gitBranch = trim(shell_exec('git branch --show-current 2>/dev/null') ?: 'unknown');
    
    echo "Current Branch: $gitBranch\n";
    echo "Latest Commit: $gitCommit\n";
    echo "Total Commits: $gitCount\n";
    
    // Get recent commits
    $recentCommits = shell_exec('git log --oneline -5 2>/dev/null');
    if ($recentCommits) {
        echo "\n📜 Recent Commits:\n";
        echo $recentCommits;
    }
} else {
    echo "⚠️  No git repository found\n";
}

echo "\n🎯 Summary:\n";
if (file_exists($versionFile)) {
    $version = json_decode(file_get_contents($versionFile), true);
    echo "The current application version is: v" . $version['version'] . "\n";
} else {
    echo "Version will be auto-detected when the application runs.\n";
}

echo "\n💡 To update the version automatically:\n";
echo "1. Make commits with descriptive messages:\n";
echo "   - 'fix: description' for patch updates\n";
echo "   - 'feat: description' for minor updates\n";
echo "   - 'breaking: description' for major updates\n";
echo "2. Push to main branch\n";
echo "3. GitHub Actions will automatically update the version\n";
?>
