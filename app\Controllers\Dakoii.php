<?php

namespace App\Controllers;

use App\Models\countryModel;
use App\Models\dakoiiUsersModel;
use App\Models\orgModel;
use App\Models\provinceModel;
use App\Models\usersModel;
use App\Models\selectionModel;

class <PERSON>koii extends BaseController
{
    public $session;
    public $dusersModel;
    public $usersModel;
    public $orgModel;
    public $countryModel;
    public $provinceModel;
    public $districtModel;
    public $llgModel;
    public $wardModel;
    public $selectionModel;

    public $educationModel;


    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->dusersModel = new dakoiiUsersModel();
        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new \App\Models\districtModel();
        $this->llgModel = new \App\Models\llgModel();
        $this->wardModel = new \App\Models\wardModel();
        $this->selectionModel = new selectionModel();

        $this->educationModel = new \App\Models\EducationModel();

    }

    // Authentication Methods
    public function index()
    {
        $data['title'] = "Dakoii Admin";
        $data['menu'] = "dlogin";
        echo view('dakoii/login', $data);
    }

    public function login()
    {
        $rules = [
            'username' => 'required',
            'password' => 'required'
        ];
        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter both username and password');
            return redirect()->to('dakoii');
        }

        $username = $this->request->getVar('username');
        $password = $this->request->getVar('password');

        $user = $this->dusersModel->where('username', $username)->first();
        if (!$user || !password_verify($password, $user['password'])) {
            session()->setFlashdata('error', 'Invalid username or password');
            return redirect()->to('dakoii');
        }

        $this->session->set([
            'logged_in' => true,
            'name' => $user['name'],
            'username' => $user['username'],
            'role' => $user['role'],
            'user_id' => $user['id']
        ]);

        session()->setFlashdata('success', 'Welcome back, ' . $user['name']);
        return redirect()->to('ddash');
    }

    public function logout()
    {
        $session = session();
        $session->destroy();
        return redirect()->to(base_url());
    }

    // Dashboard Methods
    public function ddash()
    {
        $data['title'] = "DDash";
        $data['menu'] = "ddash";

        $data['dusers'] = $this->dusersModel->findAll();
        $data['admins'] = $this->usersModel->findAll();
        $data['org'] = $this->orgModel->orderBy('id', 'DESC')->findAll();
        $data['selections'] = $this->selectionModel->orderBy('id', 'DESC')->findAll();

        $data['provinces_count'] = $this->provinceModel->countAllResults();
        $data['districts_count'] = $this->districtModel->countAllResults();
        $data['llgs_count'] = $this->llgModel->countAllResults();
        $data['wards_count'] = $this->wardModel->countAllResults();

        $data['province_stats'] = $this->getProvinceStats();


        $data['education'] = $this->educationModel->findAll();


        echo view('dakoii/ddash', $data);
    }

    private function getProvinceStats()
    {
        $provinces = $this->provinceModel->findAll();
        $stats = [];

        foreach ($provinces as $province) {
            $districts = $this->districtModel->where('province_id', $province['id'])->findAll();
            $district_ids = array_column($districts, 'id');

            $llgs_count = 0;
            $wards_count = 0;

            if (!empty($district_ids)) {
                $llgs_count = $this->llgModel->whereIn('district_id', $district_ids)->countAllResults();
                $llgs = $this->llgModel->whereIn('district_id', $district_ids)->findAll();
                $llg_ids = array_column($llgs, 'id');

                if (!empty($llg_ids)) {
                    $wards_count = $this->wardModel->whereIn('llg_id', $llg_ids)->countAllResults();
                }
            }

            $stats[$province['id']] = [
                'name' => $province['name'],
                'districts' => count($districts),
                'llgs' => $llgs_count,
                'wards' => $wards_count
            ];
        }

        return $stats;
    }

    // Organization Methods
    public function organizations()
    {
        $data['title'] = "Organizations";
        $data['menu'] = "organizations";
        $data['organizations'] = $this->orgModel->findAll();
        echo view('dakoii/organizations', $data);
    }

    public function addorg()
    {
        if ($this->validate(['name' => 'required'])) {
            $orgcode = rand(11111, 99999);
            if (!empty($this->orgModel->where('orgcode', $orgcode)->first())) {
                $orgcode = rand(11111, 99999);
            }

            $data = [
                'orgcode' => $orgcode,
                'name' => $this->request->getVar('name'),
                'description' => $this->request->getVar('description'),
                'is_active' => 1,
            ];

            $this->orgModel->insert($data);

            $logoFile = $this->request->getFile('org_logo');
            if ($logoFile->isValid() && $logoFile->getSize() > 0) {
                $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();
                $logoFile->move(ROOTPATH . 'public/uploads/org_logo/', $newName);
                $data['orglogo'] = base_url() . 'public/uploads/org_logo/' . $newName;

                $getid = $this->orgModel->where('orgcode', $orgcode)->first();
                $this->orgModel->update($getid['id'], $data);
            }

            session()->setFlashdata('success', 'Organization Created');
            return redirect()->to(base_url('dopen_org/' . $orgcode));
        }

        session()->setFlashdata('success', 'Enter valid Data');
        return redirect()->to('ddash');
    }

    public function open_org($orgcode)
    {
        $data['title'] = "Open Org";
        $data['menu'] = "openorg";

        $data['org'] = $this->orgModel->where('orgcode', $orgcode)->first();
        $data['admins'] = $this->usersModel->where('orgcode', $orgcode)->find();

        // Set org_id for the view
        $data['org_id'] = $data['org']['id'];

        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['get_provinces'] = $this->provinceModel
            ->where('country_id', $data['set_country']['id'])
            ->orderBy('name', 'asc')
            ->find();

        if (!empty($data['org']['addlockcountry'])) {
            $data['country_name'] = $this->countryModel
                ->where('id', $data['org']['addlockcountry'])
                ->get()
                ->getRow('name');
        }

        if (!empty($data['org']['addlockprov'])) {
            $data['province_name'] = $this->provinceModel
                ->where('id', $data['org']['addlockprov'])
                ->get()
                ->getRow('name');
        }

        echo view('dakoii/open_org', $data);
    }

    public function editorg()
    {
        if ($this->request->getMethod() === 'post' && $this->validate(['name' => 'required'])) {
            $id = $this->request->getVar('id');
            $orgcode = $this->request->getVar('orgcode');

            $addprov = "";
            if (!empty($this->request->getVar('country'))) {
                $addprov = $this->request->getVar('province');
            }

            $data = [
                'name' => $this->request->getVar('name'),
                'description' => $this->request->getVar('description'),
                'addlockcountry' => $this->request->getVar('country'),
                'addlockprov' => $addprov,
                'is_active' => $this->request->getVar('status'),
            ];

            $this->orgModel->update($id, $data);
        }
        return redirect()->back();
    }

    public function dakoii_set_license_status()
    {
        $id = $this->request->getVar('id');
        $orgcode = $this->request->getVar('orgcode');

        $data = [
            'license_status' => $this->request->getVar('license_status'),
        ];

        $this->orgModel->update($id, $data);

        session()->setFlashdata('success', 'License Status Changed');
        return redirect()->to('dopen_org/'.$orgcode);
    }

    // User Management Methods
    public function adduser()
    {
        if ($this->validate([
            'username' => 'required|is_unique[dakoii_users.username]',
            'password' => 'required'
        ])) {
            $is_active = !empty($this->request->getVar('is_active')) ? $this->request->getVar('is_active') : "0";

            $data = [
                'name' => $this->request->getVar('name'),
                'username' => $this->request->getVar('username'),
                'password' => password_hash($this->request->getVar('password'), PASSWORD_DEFAULT),
                'role' => $this->request->getVar('role'),
                'is_active' => $is_active,
            ];

            $this->dusersModel->insert($data);
            session()->setFlashdata('success', 'Admin Created');
            return redirect()->to('ddash');
        }

        session()->setFlashdata('error', 'Username already exist');
        return redirect()->to('ddash');
    }

    public function create_admin()
    {
        $orgcode = $this->request->getVar('orgcode');
        if ($this->validate([
            'username' => 'required|is_unique[users.username]',
            'password' => 'required'
        ])) {
            $status = !empty($this->request->getVar('is_active')) ? 1 : 0;

            // Get organization details
            $org = $this->orgModel->where('orgcode', $orgcode)->first();
            if (!$org) {
                session()->setFlashdata('error', 'Invalid organization');
                return redirect()->back();
            }

            $data = [
                'org_id' => $org['id'], // Set org_id from the organization
                'orgcode' => $orgcode,
                'name' => $this->request->getVar('name'),
                'username' => $this->request->getVar('username'),
                'password' => $this->request->getVar('password'),
                'role' => $this->request->getVar('role'),
                'status' => $status,
                'created_by' => session()->get('user_id'), // Add created_by from session
                'updated_by' => session()->get('user_id')  // Add updated_by from session
            ];

            $this->usersModel->insert($data);
            session()->setFlashdata('success', 'Organization Admin Created');
            return redirect()->to('dopen_org/'.$orgcode);
        }

        session()->setFlashdata('error', 'Username already taken');
        return redirect()->to('dopen_org/'.$orgcode);
    }

    public function editAdmin()
    {
        $id = $this->request->getVar('id');
        $orgcode = $this->request->getVar('orgcode');

        // Get organization details
        $org = $this->orgModel->where('orgcode', $orgcode)->first();
        if (!$org) {
            session()->setFlashdata('error', 'Invalid organization');
            return redirect()->back();
        }

        $rules = [
            'name' => 'required',
            'username' => 'required|is_unique[users.username,id,' . $id . ']',
            'role' => 'required'
        ];

        if ($this->validate($rules)) {
            $data = [
                'org_id' => $org['id'], // Set org_id from the organization
                'name' => $this->request->getVar('name'),
                'username' => $this->request->getVar('username'),
                'role' => $this->request->getVar('role'),
                'status' => $this->request->getVar('is_active') ? 1 : 0,
                'updated_by' => session()->get('user_id')  // Add updated_by from session
            ];

            // Handle password update
            $password = $this->request->getVar('password');
            if (!empty($password)) {
                $data['password'] = $password;
            }

            if ($this->usersModel->update($id, $data)) {
                session()->setFlashdata('success', 'Admin updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update admin');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function editSystemUser()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required',
            'username' => 'required|is_unique[dakoii_users.username,id,' . $id . ']',
            'role' => 'required'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'username' => $this->request->getPost('username'),
                'role' => $this->request->getPost('role'),
                'is_active' => $this->request->getPost('is_active') ? 1 : 0
            ];

            if (!empty($this->request->getPost('password'))) {
                $data['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
            }

            if ($this->dusersModel->update($id, $data)) {
                session()->setFlashdata('success', 'System user updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update system user');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->to('ddash');
    }

    // Province Management Methods
    public function provinces()
    {
        $data['title'] = "Provinces";
        $data['menu'] = "provinces";

        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['provinces'] = $this->provinceModel
            ->where('country_id', $data['set_country']['id'])
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/provinces', $data);
    }

    public function addProvince()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'provincecode' => 'required|is_unique[adx_province.provincecode]'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'provincecode' => $this->request->getPost('provincecode'),
                'country_id' => $this->request->getPost('country_id'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->provinceModel->insert($data)) {
                session()->setFlashdata('success', 'Province "' . $data['name'] . '" has been added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add province. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->to('provinces');
    }

    public function editProvince()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'provincecode' => 'required|is_unique[adx_province.provincecode,id,' . $id . ']'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'provincecode' => $this->request->getPost('provincecode'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->provinceModel->update($id, $data)) {
                session()->setFlashdata('success', 'Province updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update province');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->to('provinces');
    }

    public function deleteProvince($id)
    {
        if ($this->provinceModel->delete($id)) {
            session()->setFlashdata('success', 'Province deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete province');
        }
        return redirect()->to('provinces');
    }

    public function getProvince($id)
    {
        $province = $this->provinceModel->find($id);
        return $this->response->setJSON($province);
    }

    // District Management Methods
    public function districts($provinceId)
    {
        $data['title'] = "Districts";
        $data['menu'] = "provinces";

        $data['province'] = $this->provinceModel->find($provinceId);
        if (!$data['province']) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('provinces');
        }

        $data['districts'] = $this->districtModel
            ->where('province_id', $provinceId)
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/districts', $data);
    }

    public function getDistricts($provinceId)
    {
        $districts = $this->districtModel
            ->where('province_id', $provinceId)
            ->orderBy('name', 'asc')
            ->findAll();

        return $this->response->setJSON($districts);
    }

    public function addDistrict()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'districtcode' => 'required|is_unique[adx_district.districtcode]',
            'province_id' => 'required|numeric',
            'country_id' => 'required|numeric'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'districtcode' => $this->request->getPost('districtcode'),
                'province_id' => $this->request->getPost('province_id'),
                'country_id' => $this->request->getPost('country_id'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->districtModel->insert($data)) {
                session()->setFlashdata('success', 'District "' . $data['name'] . '" has been added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add district. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function editDistrict()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'districtcode' => 'required|is_unique[adx_district.districtcode,id,' . $id . ']'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'districtcode' => $this->request->getPost('districtcode'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->districtModel->update($id, $data)) {
                session()->setFlashdata('success', 'District updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update district');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function deleteDistrict($id)
    {
        $district = $this->districtModel->find($id);
        if ($district) {
            if ($this->districtModel->delete($id)) {
                session()->setFlashdata('success', 'District "' . $district['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete district. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'District not found');
        }
        return redirect()->back();
    }

    // LLG Management Methods
    public function llgs($districtId)
    {
        $data['title'] = "LLGs";
        $data['menu'] = "provinces";

        $data['district'] = $this->districtModel->find($districtId);
        if (!$data['district']) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('provinces');
        }

        $data['province'] = $this->provinceModel->find($data['district']['province_id']);
        $data['llgs'] = $this->llgModel
            ->where('district_id', $districtId)
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/llgs', $data);
    }

    public function addLLG()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'llgcode' => 'required|is_unique[adx_llg.llgcode]',
            'district_id' => 'required|numeric',
            'province_id' => 'required|numeric',
            'country_id' => 'required|numeric'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'llgcode' => $this->request->getPost('llgcode'),
                'district_id' => $this->request->getPost('district_id'),
                'province_id' => $this->request->getPost('province_id'),
                'country_id' => $this->request->getPost('country_id'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->llgModel->insert($data)) {
                session()->setFlashdata('success', 'LLG "' . $data['name'] . '" has been added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add LLG. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function editLLG()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'llgcode' => 'required|is_unique[adx_llg.llgcode,id,' . $id . ']'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'llgcode' => $this->request->getPost('llgcode'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->llgModel->update($id, $data)) {
                session()->setFlashdata('success', 'LLG updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update LLG');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function deleteLLG($id)
    {
        $llg = $this->llgModel->find($id);
        if ($llg) {
            if ($this->llgModel->delete($id)) {
                session()->setFlashdata('success', 'LLG "' . $llg['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete LLG. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'LLG not found');
        }
        return redirect()->back();
    }

    // Ward Management Methods
    public function wards($llgId)
    {
        $data['title'] = "Wards";
        $data['menu'] = "provinces";

        $data['llg'] = $this->llgModel->find($llgId);
        if (!$data['llg']) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('provinces');
        }

        $data['district'] = $this->districtModel->find($data['llg']['district_id']);
        $data['province'] = $this->provinceModel->find($data['district']['province_id']);

        $data['wards'] = $this->wardModel
            ->where('llg_id', $llgId)
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/wards', $data);
    }

    public function addWard()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'wardcode' => 'required|is_unique[adx_ward.wardcode]',
            'llg_id' => 'required|numeric',
            'district_id' => 'required|numeric',
            'province_id' => 'required|numeric',
            'country_id' => 'required|numeric'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'wardcode' => $this->request->getPost('wardcode'),
                'llg_id' => $this->request->getPost('llg_id'),
                'district_id' => $this->request->getPost('district_id'),
                'province_id' => $this->request->getPost('province_id'),
                'country_id' => $this->request->getPost('country_id')
            ];

            if ($this->wardModel->insert($data)) {
                session()->setFlashdata('success', 'Ward "' . $data['name'] . '" has been added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add ward. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function editWard()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'wardcode' => 'required|is_unique[adx_ward.wardcode,id,' . $id . ']'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'wardcode' => $this->request->getPost('wardcode')
            ];

            if ($this->wardModel->update($id, $data)) {
                session()->setFlashdata('success', 'Ward updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update ward');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function deleteWard($id)
    {
        $ward = $this->wardModel->find($id);
        if ($ward) {
            if ($this->wardModel->delete($id)) {
                session()->setFlashdata('success', 'Ward "' . $ward['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete ward. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Ward not found');
        }
        return redirect()->back();
    }



    // Education
    public function addEducation()
    {
        if ($this->request->getMethod() === 'post') {
            $data = [
                'name' => $this->request->getPost('name'),
                'icon' => $this->request->getPost('icon'),
                'color_code' => $this->request->getPost('color_code'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => session()->get('user_id')
            ];

            if ($this->educationModel->insert($data)) {
                session()->setFlashdata('success', 'Education item added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add education item');
            }
        }
        return redirect()->to('ddash');
    }

    public function updateEducation()
    {
        $id = $this->request->getPost('id');
        $data = [
            'name' => $this->request->getPost('name'),
            'icon' => $this->request->getPost('icon'),
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('user_id')
        ];

        if ($this->educationModel->update($id, $data)) {
            session()->setFlashdata('success', 'Education item updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update education item');
        }
        return redirect()->to('ddash');
    }

}
