<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>Salary Analysis
                </h5>
            </div>
            <div class="card-body">
                <!-- Payslip Selection Form -->
                <form id="payslipAnalysisForm" method="post" action="<?= site_url('reports/salary') ?>">
                    <?= csrf_field() ?>
                    <div class="row mb-4">
                        <div class="col-md-5">
                            <label for="start_payslip" class="form-label">Start Payslip</label>
                            <select name="start_payslip" id="start_payslip" class="form-select">
                                <?php foreach ($payslips as $payslip): ?>
                                    <option value="<?= $payslip['id'] ?>" <?= isset($selected_start) && $selected_start == $payslip['id'] ? 'selected' : '' ?>>
                                        <?= esc($payslip['pay_no']) ?> (<?= date('d M Y', strtotime($payslip['pay_date'])) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label for="end_payslip" class="form-label">End Payslip</label>
                            <select name="end_payslip" id="end_payslip" class="form-select">
                                <?php foreach ($payslips as $payslip): ?>
                                    <option value="<?= $payslip['id'] ?>" <?= isset($selected_end) && $selected_end == $payslip['id'] ? 'selected' : '' ?>>
                                        <?= esc($payslip['pay_no']) ?> (<?= date('d M Y', strtotime($payslip['pay_date'])) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-chart-bar me-2"></i>Analyze Payslips
                            </button>
                        </div>
                    </div>
                </form>
                
                <?php if (isset($analysis) && $analysis['success']): ?>
                    <!-- Analysis Results -->
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        Successfully analyzed <?= $analysis['payslip_count'] ?> payslip(s) from 
                        <?= date('d M Y', strtotime($start_payslip['pay_date'])) ?> 
                        to 
                        <?= date('d M Y', strtotime($end_payslip['pay_date'])) ?>.
                    </div>
                    
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                The summary below shows the total amounts across all analyzed payslips and all pages within each payslip.
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-muted mb-0">Total Base Salary</h6>
                                    <h3 class="mt-2 mb-0"><?= number_format($analysis['total_base_salary'], 2) ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-muted mb-0">Total Overtime</h6>
                                    <h3 class="mt-2 mb-0"><?= number_format($analysis['total_overtime'], 2) ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-muted mb-0">Total Gross Pay</h6>
                                    <h3 class="mt-2 mb-0"><?= number_format($analysis['total_gross'], 2) ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-muted mb-0">Total Net Pay</h6>
                                    <h3 class="mt-2 mb-0 text-success"><?= number_format($analysis['total_net'], 2) ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-4 mb-3">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-muted mb-0">Total Tax</h6>
                                    <h3 class="mt-2 mb-0 text-danger"><?= number_format($analysis['total_tax'], 2) ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-muted mb-0">Total Deductions</h6>
                                    <h3 class="mt-2 mb-0 text-danger"><?= number_format($analysis['total_deductions'], 2) ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h6 class="card-title text-muted mb-0">Total Super</h6>
                                    <h3 class="mt-2 mb-0"><?= number_format($analysis['total_super'], 2) ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Charts -->
                    <div class="row mt-4">
                        <div class="col-md-6 mb-4">
                            <div class="card shadow-sm">
                                <div class="card-header bg-white">
                                    <h6 class="card-title mb-0">Total Salary Distribution</h6>
                                    <small class="text-muted">Distribution of total amounts across all payslips</small>
                                </div>
                                <div class="card-body">
                                    <div id="salaryDistributionChart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card shadow-sm">
                                <div class="card-header bg-white">
                                    <h6 class="card-title mb-0">Monthly Salary Trends</h6>
                                    <small class="text-muted">Monthly totals across all payslips</small>
                                </div>
                                <div class="card-body">
                                    <div id="salaryTrendChart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Payslips Table -->
                    <div class="card shadow-sm mt-4">
                        <div class="card-header bg-white">
                            <h6 class="card-title mb-0">Analyzed Payslips</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Pay No</th>
                                            <th>Pay Date</th>
                                            <th>Base Salary</th>
                                            <th>Overtime</th>
                                            <th>Gross</th>
                                            <th>Tax</th>
                                            <th>Deductions</th>
                                            <th>Super</th>
                                            <th>Net</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($analysis['payslips'] as $payslipIndex => $payslip): ?>
                                            <tr class="table-primary">
                                                <td colspan="9" class="fw-bold">
                                                    <i class="fas fa-file-invoice me-2"></i>
                                                    Payslip: <?= esc($payslip['pay_no']) ?> (<?= date('d M Y', strtotime($payslip['pay_date'])) ?>)
                                                </td>
                                            </tr>
                                            
                                            <?php if (isset($payslip['data']['pages_data'])): ?>
                                                <?php foreach ($payslip['data']['pages_data'] as $pageIndex => $pageData): ?>
                                                    <tr>
                                                        <td>
                                                            <small class="text-muted">Page <?= $pageIndex + 1 ?></small>
                                                        </td>
                                                        <td>-</td>
                                                        <td><?= number_format($pageData['base_salary'], 2) ?></td>
                                                        <td><?= number_format($pageData['overtime'], 2) ?></td>
                                                        <td><?= number_format($pageData['gross'], 2) ?></td>
                                                        <td><?= number_format($pageData['tax'], 2) ?></td>
                                                        <td><?= number_format($pageData['deductions'], 2) ?></td>
                                                        <td><?= number_format($pageData['super'], 2) ?></td>
                                                        <td><?= number_format($pageData['net'], 2) ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                                <tr class="table-light">
                                                    <td colspan="2" class="fw-bold">Payslip Total</td>
                                                    <td class="fw-bold"><?= number_format($payslip['data']['base_salary'], 2) ?></td>
                                                    <td class="fw-bold"><?= number_format($payslip['data']['overtime'], 2) ?></td>
                                                    <td class="fw-bold"><?= number_format($payslip['data']['gross'], 2) ?></td>
                                                    <td class="fw-bold"><?= number_format($payslip['data']['tax'], 2) ?></td>
                                                    <td class="fw-bold"><?= number_format($payslip['data']['deductions'], 2) ?></td>
                                                    <td class="fw-bold"><?= number_format($payslip['data']['super'], 2) ?></td>
                                                    <td class="fw-bold"><?= number_format($payslip['data']['net'], 2) ?></td>
                                                </tr>
                                            <?php else: ?>
                                                <tr>
                                                    <td>-</td>
                                                    <td>-</td>
                                                    <td><?= number_format($payslip['data']['base_salary'], 2) ?></td>
                                                    <td><?= number_format($payslip['data']['overtime'], 2) ?></td>
                                                    <td><?= number_format($payslip['data']['gross'], 2) ?></td>
                                                    <td><?= number_format($payslip['data']['tax'], 2) ?></td>
                                                    <td><?= number_format($payslip['data']['deductions'], 2) ?></td>
                                                    <td><?= number_format($payslip['data']['super'], 2) ?></td>
                                                    <td><?= number_format($payslip['data']['net'], 2) ?></td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot class="table-dark">
                                        <tr class="fw-bold">
                                            <td colspan="2">Grand Total</td>
                                            <td><?= number_format($analysis['total_base_salary'], 2) ?></td>
                                            <td><?= number_format($analysis['total_overtime'], 2) ?></td>
                                            <td><?= number_format($analysis['total_gross'], 2) ?></td>
                                            <td><?= number_format($analysis['total_tax'], 2) ?></td>
                                            <td><?= number_format($analysis['total_deductions'], 2) ?></td>
                                            <td><?= number_format($analysis['total_super'], 2) ?></td>
                                            <td><?= number_format($analysis['total_net'], 2) ?></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php elseif (isset($analysis) && !$analysis['success']): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= $analysis['message'] ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Select payslips and click "Analyze Payslips" to view salary analysis.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Charts -->
<script type="text/javascript">
    // Load Google Charts
    google.charts.load('current', {'packages':['corechart']});
    
    <?php if (isset($analysis) && $analysis['success']): ?>
    // Set a callback to run when the Google Visualization API is loaded
    google.charts.setOnLoadCallback(drawCharts);
    
    function drawCharts() {
        // Draw Salary Distribution Chart
        drawSalaryDistributionChart();
        
        // Draw Salary Trend Chart
        drawSalaryTrendChart();
    }
    
    function drawSalaryDistributionChart() {
        // Create the data table
        var data = new google.visualization.DataTable();
        data.addColumn('string', 'Category');
        data.addColumn('number', 'Amount');
        data.addRows([
            ['Base Salary', <?= $analysis['total_base_salary'] ?>],
            ['Overtime', <?= $analysis['total_overtime'] ?>],
            ['Tax', <?= $analysis['total_tax'] ?>],
            ['Deductions', <?= $analysis['total_deductions'] ?>],
            ['Super', <?= $analysis['total_super'] ?>]
        ]);
        
        // Set chart options
        var options = {
            title: 'Total Salary Distribution',
            pieHole: 0.4,
            colors: ['#4e73df', '#1cc88a', '#e74a3b', '#f6c23e', '#36b9cc'],
            legend: { position: 'right' },
            chartArea: { width: '80%', height: '80%' },
            tooltip: { text: 'value' }
        };
        
        // Instantiate and draw the chart
        var chart = new google.visualization.PieChart(document.getElementById('salaryDistributionChart'));
        chart.draw(data, options);
    }
    
    function drawSalaryTrendChart() {
        // Create the data table
        var data = new google.visualization.DataTable();
        data.addColumn('string', 'Month');
        data.addColumn('number', 'Gross');
        data.addColumn('number', 'Net');
        
        <?php foreach ($analysis['chart_data']['labels'] as $index => $label): ?>
        data.addRow([
            '<?= $label ?>', 
            <?= $analysis['chart_data']['gross'][$index] ?>, 
            <?= $analysis['chart_data']['net'][$index] ?>
        ]);
        <?php endforeach; ?>
        
        // Set chart options
        var options = {
            title: 'Monthly Salary Trends',
            curveType: 'function',
            legend: { position: 'bottom' },
            colors: ['#4e73df', '#1cc88a'],
            chartArea: { width: '80%', height: '70%' },
            hAxis: { title: 'Month' },
            vAxis: { title: 'Amount' },
            tooltip: { showColorCode: true }
        };
        
        // Instantiate and draw the chart
        var chart = new google.visualization.LineChart(document.getElementById('salaryTrendChart'));
        chart.draw(data, options);
    }
    <?php endif; ?>
    
    // Wait for the document to be fully loaded before using jQuery
    document.addEventListener('DOMContentLoaded', function() {
        // Make sure jQuery is available
        if (typeof jQuery !== 'undefined') {
            // AJAX form submission
            jQuery(document).ready(function($) {
                // Optional: AJAX form submission
                $('#payslipAnalysisForm').on('submit', function(e) {
                    // Uncomment to enable AJAX submission
                    /*
                    e.preventDefault();
                    
                    $.ajax({
                        url: '<?= site_url('salaries/getSalaryAnalysisData') ?>',
                        type: 'POST',
                        data: $(this).serialize(),
                        dataType: 'json',
                        beforeSend: function() {
                            // Show loading indicator
                            Swal.fire({
                                title: 'Analyzing...',
                                text: 'Please wait while we analyze your payslips',
                                allowOutsideClick: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                }
                            });
                        },
                        success: function(response) {
                            Swal.close();
                            
                            if (response.error) {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: response.error
                                });
                            } else {
                                // Reload the page with the new data
                                location.reload();
                            }
                        },
                        error: function() {
                            Swal.close();
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'An error occurred while processing your request'
                            });
                        }
                    });
                    */
                });
            });
        } else {
            console.error('jQuery is not loaded. Some features may not work properly.');
        }
    });
</script>
<?= $this->endSection() ?>
