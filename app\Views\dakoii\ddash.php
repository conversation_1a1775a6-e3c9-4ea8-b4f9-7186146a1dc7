<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h4 class="mb-0">Dakoii Dashboard</h4>
        <div class="d-flex gap-2">
            <a href="<?= base_url('dakoii/version') ?>" class="btn btn-success">
                <i class="fas fa-code-branch"></i> Version Management
            </a>
            <a href="<?= base_url('dakoii-leave') ?>" class="btn btn-info">
                <i class="fas fa-calendar-alt"></i> Manage Leave Types
            </a>
            <button class="btn btn-primary" data-toggle="modal" data-target="#modelId">
                <i class="fas fa-user-plus"></i> Add System User
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Organizations Stats -->
        <div class="col-md-3">
            <div class="card bg-primary text-white shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0">Organizations</h6>
                            <h2 class="mt-2 mb-0"><?= count($org) ?></h2>
                        </div>
                        <div class="rounded-circle bg-white p-3">
                            <i class="fas fa-building text-primary"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small>
                            Active: <?= count(array_filter($org, fn($o) => $o['is_active'] == 1)) ?>
                            | Paid: <?= count(array_filter($org, fn($o) => $o['license_status'] == 'paid')) ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Administrative Areas Stats -->
        <div class="col-md-3">
            <div class="card bg-success text-white shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0">Administrative Areas</h6>
                            <h2 class="mt-2 mb-0"><?= $provinces_count ?></h2>
                        </div>
                        <div class="rounded-circle bg-white p-3">
                            <i class="fas fa-map-marker-alt text-success"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small>
                            Districts: <?= $districts_count ?> |
                            LLGs: <?= $llgs_count ?> |
                            Wards: <?= $wards_count ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Users Stats -->
        <div class="col-md-3">
            <div class="card bg-info text-white shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0">System Users</h6>
                            <h2 class="mt-2 mb-0"><?= count($dusers) ?></h2>
                        </div>
                        <div class="rounded-circle bg-white p-3">
                            <i class="fas fa-users text-info"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small>Active: <?= count(array_filter($dusers, fn($u) => $u['is_active'] == 1)) ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Organization Admins Stats -->
        <div class="col-md-3">
            <div class="card bg-warning text-white shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0">Org Admins</h6>
                            <h2 class="mt-2 mb-0"><?= count($admins) ?></h2>
                        </div>
                        <div class="rounded-circle bg-white p-3">
                            <i class="fas fa-user-shield text-warning"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small>Total Organization Administrators</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Organizations Table -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Organizations</h5>
                    <a href="<?= base_url('organizations') ?>" class="btn btn-sm btn-primary">
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Code</th>
                                    <th>License</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($org, 0, 5) as $o): ?>
                                <tr>
                                    <td>
                                        <?php if ($o['orglogo']): ?>
                                            <img src="<?php imgcheck($o['orglogo']) ?>" alt="Logo" class="img-thumbnail mr-2" style="height: 30px;">
                                        <?php endif; ?>
                                        <?= esc($o['name']) ?>
                                    </td>
                                    <td><?= esc($o['orgcode']) ?></td>
                                    <td>
                                        <span class="badge badge-<?= $o['license_status'] == 'paid' ? 'success' : 'warning' ?>">
                                            <?= ucfirst($o['license_status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $o['is_active'] ? 'success' : 'danger' ?>">
                                            <?= $o['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('dopen_org/' . $o['orgcode']) ?>"
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Users Table -->
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">System Users</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($dusers as $user): ?>
                                <tr>
                                    <td><?= esc($user['name']) ?></td>
                                    <td>
                                        <span class="badge badge-info">
                                            <?= ucfirst($user['role']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $user['is_active'] ? 'success' : 'danger' ?>">
                                            <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary edit-system-user"
                                                data-id="<?= $user['id'] ?>"
                                                data-name="<?= esc($user['name']) ?>"
                                                data-username="<?= esc($user['username']) ?>"
                                                data-role="<?= esc($user['role']) ?>"
                                                data-active="<?= $user['is_active'] ?>"
                                                data-toggle="modal"
                                                data-target="#editSystemUserModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Administrative Areas Overview -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Administrative Areas Overview</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Province</th>
                                    <th>Districts</th>
                                    <th>LLGs</th>
                                    <th>Wards</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($province_stats as $stat): ?>
                                <tr>
                                    <td><?= esc($stat['name']) ?></td>
                                    <td>
                                        <span class="badge badge-info">
                                            <?= number_format($stat['districts']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success">
                                            <?= number_format($stat['llgs']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">
                                            <?= number_format($stat['wards']) ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot class="bg-light">
                                <tr>
                                    <th>Total</th>
                                    <th><?= number_format($districts_count) ?></th>
                                    <th><?= number_format($llgs_count) ?></th>
                                    <th><?= number_format($wards_count) ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="modelId" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i> Add System User
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('dadduser') ?>
            <div class="modal-body">
                <div class="form-group">
                    <label>Name</label>
                    <input type="text" class="form-control" name="name" required>
                </div>
                <div class="form-group">
                    <label>Username</label>
                    <input type="text" class="form-control" name="username" required>
                </div>
                <div class="form-group">
                    <label>Password</label>
                    <input type="password" class="form-control" name="password" required>
                </div>
                <div class="form-group">
                    <label>Role</label>
                    <select class="form-control" name="role" required>
                        <option value="user">User</option>
                        <option value="moderator">Moderator</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1" checked>
                    <label class="custom-control-label" for="is_active">Active Account</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Create User
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit System User Modal -->
<div class="modal fade" id="editSystemUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit"></i> Edit System User
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('edit-system-user') ?>
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_user_id">
                <div class="form-group">
                    <label>Name</label>
                    <input type="text" class="form-control" name="name" id="edit_user_name" required>
                </div>
                <div class="form-group">
                    <label>Username</label>
                    <input type="text" class="form-control" name="username" id="edit_user_username" required>
                </div>
                <div class="form-group">
                    <label>New Password</label>
                    <input type="password" class="form-control" name="password" placeholder="Leave blank to keep current password">
                    <small class="form-text text-muted">Only fill this if you want to change the password</small>
                </div>
                <div class="form-group">
                    <label>Role</label>
                    <select class="form-control" name="role" id="edit_user_role" required>
                        <option value="user">User</option>
                        <option value="moderator">Moderator</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="edit_user_active" name="is_active" value="1">
                    <label class="custom-control-label" for="edit_user_active">Active Account</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update User
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0,0,0,.125);
}
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}
.table td, .table th {
    vertical-align: middle;
    padding: 0.75rem;
}
.rounded-circle {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.rounded-circle i {
    font-size: 1.5rem;
}
.img-thumbnail {
    padding: 0.25rem;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    max-width: 100%;
    height: auto;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit system user
    document.querySelectorAll('.edit-system-user').forEach(button => {
        button.addEventListener('click', function() {
            document.getElementById('edit_user_id').value = this.dataset.id;
            document.getElementById('edit_user_name').value = this.dataset.name;
            document.getElementById('edit_user_username').value = this.dataset.username;
            document.getElementById('edit_user_role').value = this.dataset.role;
            document.getElementById('edit_user_active').checked = this.dataset.active === '1';
        });
    });
});
</script>

<?= $this->endSection() ?>
