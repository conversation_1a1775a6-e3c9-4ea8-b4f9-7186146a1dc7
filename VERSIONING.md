# Application Versioning System

This application uses a semantic versioning system (MAJOR.MINOR.PATCH) to track releases and changes.

## Version Format

The version follows the format: `MAJOR.MINOR.PATCH`

- **MAJOR**: Incremented for breaking changes or major system upgrades
- **MINOR**: Incremented for new features and backward-compatible changes  
- **PATCH**: Incremented for bug fixes and minor updates

## Version Management

### Web Interface

Access the version management interface through the Dakoii admin portal:

1. Login to Dakoii admin
2. Go to Dashboard
3. Click "Version Management" button
4. Use the interface to increment or manually set versions

### Command Line Interface

Use the Spark command to manage versions:

```bash
# Show current version
php spark version show

# Increment patch version (1.0.0 → 1.0.1)
php spark version increment --type=patch --notes="Bug fixes"

# Increment minor version (1.0.1 → 1.1.0)
php spark version increment --type=minor --notes="New features added"

# Increment major version (1.1.0 → 2.0.0)
php spark version increment --type=major --notes="Breaking changes"

# Set specific version
php spark version set --major=2 --minor=1 --patch=0 --notes="Custom version"
```

### GitHub Actions

The repository includes automated versioning through GitHub Actions:

#### Manual Version Update
1. Go to Actions tab in GitHub
2. Select "Version Management" workflow
3. Click "Run workflow"
4. Choose version type (patch/minor/major)
5. Add release notes
6. Run the workflow

#### Automatic Patch Updates
- Patch versions are automatically incremented on commits to main branch
- Creates git tags and GitHub releases automatically

## Version Storage

Version information is stored in:
- **File**: `app/Views/dakoii/version.json`
- **Format**: JSON with version details, release notes, and build information

## Integration

The version system integrates with:

1. **Constants**: Available as `SYSTEM_VERSION` constant throughout the application
2. **Templates**: Displayed in footer of admin templates
3. **API**: Available via `/dakoii/version/json` endpoint
4. **Helpers**: Version helper functions available globally

## Version Guidelines

### When to increment MAJOR version:
- Breaking changes to existing functionality
- Major system architecture changes
- Incompatible API changes
- Complete feature overhauls

### When to increment MINOR version:
- New features added
- New functionality that's backward compatible
- Significant enhancements to existing features
- New API endpoints

### When to increment PATCH version:
- Bug fixes
- Security patches
- Minor improvements
- Documentation updates
- Performance optimizations

## Files Modified

The versioning system affects these files:

- `app/Views/dakoii/version.json` - Version data storage
- `app/Helpers/Version_helper.php` - Version management functions
- `app/Config/Constants.php` - System version constant
- `app/Controllers/DakoiiVersionController.php` - Web interface controller
- `app/Views/dakoii/version_management.php` - Admin interface
- `app/Commands/VersionCommand.php` - CLI commands
- `.github/workflows/version-management.yml` - GitHub Actions workflow

## Best Practices

1. **Always add release notes** when updating versions
2. **Use patch increments** for small fixes and updates
3. **Use minor increments** for new features
4. **Use major increments** sparingly for significant changes
5. **Test thoroughly** before major version releases
6. **Document breaking changes** in major version release notes

## Troubleshooting

### Version file not found
If the version file is missing, it will be created with default values (1.0.0).

### Permission issues
Ensure the web server has write permissions to `app/Views/dakoii/` directory.

### GitHub Actions not working
Check that the repository has the necessary secrets and permissions for GitHub Actions.

## API Endpoints

- `GET /dakoii/version/json` - Returns current version information as JSON
- `POST /dakoii/version/increment` - Increment version (admin only)
- `POST /dakoii/version/update` - Set specific version (admin only)

## Example Version History

```
v1.0.0 - Initial release
v1.0.1 - Bug fixes and minor improvements
v1.1.0 - Added employee portal features
v1.1.1 - Security patches
v2.0.0 - Major system upgrade with new architecture
```
