# Automated Application Versioning System

This application uses a **fully automated** semantic versioning system (MAJOR.MINOR.PATCH) that requires **zero manual intervention**.

## How It Works

The versioning system automatically:
- 🤖 Analyzes your git commit messages
- 📈 Determines the appropriate version increment
- 🔄 Updates the version file automatically
- 🏷️ Creates git tags and GitHub releases
- 📝 Generates release notes from commits

## Version Format

The version follows the format: `MAJOR.MINOR.PATCH`

- **MAJOR**: Auto-incremented for breaking changes (`breaking:`, `major:` in commits)
- **MINOR**: Auto-incremented for new features (`feat:`, `feature:`, `add:`, `new:` in commits)
- **PATCH**: Auto-incremented for all other commits (bug fixes, improvements, etc.)

## Commit Message Format

Use these prefixes in your commit messages to control versioning:

```bash
# Patch increment (1.0.0 → 1.0.1)
git commit -m "fix: resolve login issue"
git commit -m "docs: update README"
git commit -m "style: improve button styling"

# Minor increment (1.0.1 → 1.1.0)
git commit -m "feat: add user dashboard"
git commit -m "feature: implement email notifications"
git commit -m "add: new reporting module"

# Major increment (1.1.0 → 2.0.0)
git commit -m "breaking: change API structure"
git commit -m "major: redesign database schema"
```

## Automatic Process

### When You Push to Main Branch:
1. 🔍 GitHub Actions analyzes recent commits
2. 🧮 Calculates new version based on commit messages
3. 📄 Updates `app/Views/dakoii/version.json`
4. 🏷️ Creates git tag (e.g., `v1.2.3`)
5. 🚀 Creates GitHub release with auto-generated notes
6. ✅ Version is live - no manual work needed!

## Version Storage

Version information is automatically stored in:
- **File**: `app/Views/dakoii/version.json`
- **Format**: JSON with version details, auto-generated release notes, and build information
- **Updates**: Automatically updated on every push to main branch

## Integration

The version system automatically integrates with:

1. **Constants**: Available as `SYSTEM_VERSION` constant throughout the application
2. **Templates**: Displayed in footer of admin templates with build info
3. **Helpers**: Version helper functions available globally
4. **Git**: Uses git commit count as build number and commit hash for tracking

## Files in the System

The automated versioning system includes:

- `app/Views/dakoii/version.json` - Auto-updated version data storage
- `app/Helpers/Version_helper.php` - Automated version detection functions
- `app/Config/Constants.php` - System version constant (reads from version.json)
- `.github/workflows/version-management.yml` - Automated GitHub Actions workflow

## Best Practices for Commits

1. **Use descriptive commit prefixes** to control version increments
2. **Write clear commit messages** - they become your release notes
3. **Group related changes** in single commits when possible
4. **Use `feat:` for new features** you want users to know about
5. **Use `fix:` for bug fixes** that should be documented
6. **Use `breaking:` sparingly** for major changes that break compatibility

## Examples of Good Commit Messages

```bash
# These will increment PATCH version
git commit -m "fix: resolve user login timeout issue"
git commit -m "docs: update installation instructions"
git commit -m "style: improve responsive design on mobile"
git commit -m "perf: optimize database queries"

# These will increment MINOR version
git commit -m "feat: add employee photo upload feature"
git commit -m "feature: implement advanced search filters"
git commit -m "add: new payroll calculation module"
git commit -m "new: dashboard analytics widgets"

# These will increment MAJOR version
git commit -m "breaking: change user authentication system"
git commit -m "major: redesign database structure for performance"
```

## Troubleshooting

### Version not updating
- Check that commits are being pushed to the main/master branch
- Verify GitHub Actions are enabled in your repository
- Check the Actions tab for any workflow failures

### Git not available
- The system falls back to stored version.json if git is not available
- Ensure git is installed in your deployment environment

### Permission issues
- Ensure the web server has read permissions to `app/Views/dakoii/` directory
- The system will create the directory if it doesn't exist

## Example Automated Version History

```
v1.0.0 - Initial release with automated versioning
v1.0.1 - fix: resolve login timeout issue
v1.0.2 - docs: update user guide, fix: minor UI bugs
v1.1.0 - feat: add employee photo upload feature
v1.1.1 - fix: resolve photo upload validation
v1.2.0 - feat: implement advanced search, add: new reporting
v2.0.0 - breaking: redesign authentication system
```

## Zero Maintenance Required! 🎉

Once set up, this system requires **absolutely no manual intervention**. Just write good commit messages and push your code - the versioning happens automatically!
