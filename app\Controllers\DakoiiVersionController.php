<?php

namespace App\Controllers;

class DakoiiVersionController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'version']);
        $this->session = session();
    }

    /**
     * Check if user is authenticated as dakoii admin
     */
    private function checkAuth()
    {
        if (!$this->session->get('logged_in') || $this->session->get('role') !== 'admin') {
            return redirect()->to('dakoii')->with('error', 'Access denied. Please login as admin.');
        }
        return true;
    }

    /**
     * Display version management page
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $data['title'] = "Version Management";
        $data['menu'] = "version";
        
        // Get current version information
        $data['versionInfo'] = get_version_info();
        $currentVersion = get_app_version();
        $data['currentVersion'] = $currentVersion ?: ['major' => 1, 'minor' => 0, 'patch' => 0];

        return view('dakoii/version_management', $data);
    }

    /**
     * Increment version (patch, minor, or major)
     */
    public function increment()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $rules = [
            'increment_type' => 'required|in_list[patch,minor,major]',
            'release_notes' => 'permit_empty|string'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('error', 'Please select a valid increment type.');
        }

        $incrementType = $this->request->getPost('increment_type');
        $releaseNotes = $this->request->getPost('release_notes') ?: '';

        if (increment_version($incrementType, $releaseNotes)) {
            $newVersion = get_version_string();
            
            // Log the version change
            log_message('info', "Version incremented to {$newVersion} by user " . $this->session->get('username'));
            
            return redirect()->to('dakoii/version')->with('success', "Version successfully incremented to {$newVersion}");
        } else {
            return redirect()->back()->with('error', 'Failed to increment version. Please try again.');
        }
    }

    /**
     * Update version manually
     */
    public function update()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $rules = [
            'major' => 'required|integer|greater_than_equal_to[0]',
            'minor' => 'required|integer|greater_than_equal_to[0]',
            'patch' => 'required|integer|greater_than_equal_to[0]',
            'release_notes' => 'permit_empty|string'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('error', 'Please provide valid version numbers.');
        }

        $major = (int) $this->request->getPost('major');
        $minor = (int) $this->request->getPost('minor');
        $patch = (int) $this->request->getPost('patch');
        $releaseNotes = $this->request->getPost('release_notes') ?: '';

        if (update_app_version($major, $minor, $patch, $releaseNotes)) {
            $newVersion = "{$major}.{$minor}.{$patch}";
            
            // Log the version change
            log_message('info', "Version manually updated to {$newVersion} by user " . $this->session->get('username'));
            
            return redirect()->to('dakoii/version')->with('success', "Version successfully updated to {$newVersion}");
        } else {
            return redirect()->back()->with('error', 'Failed to update version. Please try again.');
        }
    }

    /**
     * Get version information as JSON (for API calls)
     */
    public function getVersionJson()
    {
        $versionInfo = get_version_info();
        return $this->response->setJSON($versionInfo);
    }

    /**
     * Create a GitHub release (if GitHub integration is set up)
     */
    public function createGitHubRelease()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $version = get_app_version();
        if (!$version) {
            return redirect()->back()->with('error', 'Unable to read current version.');
        }

        // This would integrate with GitHub API to create a release
        // For now, we'll just provide instructions
        $instructions = "To create a GitHub release:\n\n";
        $instructions .= "1. Go to your GitHub repository\n";
        $instructions .= "2. Click on 'Releases' tab\n";
        $instructions .= "3. Click 'Create a new release'\n";
        $instructions .= "4. Use tag: v{$version['version']}\n";
        $instructions .= "5. Title: Release {$version['version']}\n";
        $instructions .= "6. Description: {$version['release_notes']}\n";

        return redirect()->back()->with('success', $instructions);
    }
}
