<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-code-branch"></i> Version Management
                    </h3>
                </div>
                <div class="card-body">
                    
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Current Version Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h4 class="card-title">Current Version</h4>
                                    <h2 class="mb-0"><?= $versionInfo['version'] ?></h2>
                                    <small>Build #<?= $versionInfo['build_number'] ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h4 class="card-title">Release Date</h4>
                                    <h5 class="mb-0"><?= $versionInfo['release_date'] ?></h5>
                                    <small>Last updated: <?= date('M d, Y H:i', strtotime($versionInfo['last_updated'])) ?></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Release Notes -->
                    <?php if (!empty($versionInfo['release_notes'])): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Current Release Notes</h5>
                                </div>
                                <div class="card-body">
                                    <p class="mb-0"><?= esc($versionInfo['release_notes']) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Version Update Form -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Quick Version Increment</h5>
                                </div>
                                <div class="card-body">
                                    <form action="<?= base_url('dakoii/version/increment') ?>" method="post">
                                        <?= csrf_field() ?>
                                        
                                        <div class="mb-3">
                                            <label for="increment_type" class="form-label">Increment Type</label>
                                            <select class="form-select" id="increment_type" name="increment_type" required>
                                                <option value="">Select increment type</option>
                                                <option value="patch">Patch (Bug fixes) - <?= $currentVersion['major'] ?>.<?= $currentVersion['minor'] ?>.<?= $currentVersion['patch'] + 1 ?></option>
                                                <option value="minor">Minor (New features) - <?= $currentVersion['major'] ?>.<?= $currentVersion['minor'] + 1 ?>.0</option>
                                                <option value="major">Major (Breaking changes) - <?= $currentVersion['major'] + 1 ?>.0.0</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="release_notes" class="form-label">Release Notes</label>
                                            <textarea class="form-control" id="release_notes" name="release_notes" rows="3" placeholder="Describe what's new in this version..."></textarea>
                                        </div>

                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-arrow-up"></i> Increment Version
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Manual Version Update</h5>
                                </div>
                                <div class="card-body">
                                    <form action="<?= base_url('dakoii/version/update') ?>" method="post">
                                        <?= csrf_field() ?>
                                        
                                        <div class="row">
                                            <div class="col-4">
                                                <div class="mb-3">
                                                    <label for="major" class="form-label">Major</label>
                                                    <input type="number" class="form-control" id="major" name="major" value="<?= $currentVersion['major'] ?>" min="0" required>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="mb-3">
                                                    <label for="minor" class="form-label">Minor</label>
                                                    <input type="number" class="form-control" id="minor" name="minor" value="<?= $currentVersion['minor'] ?>" min="0" required>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="mb-3">
                                                    <label for="patch" class="form-label">Patch</label>
                                                    <input type="number" class="form-control" id="patch" name="patch" value="<?= $currentVersion['patch'] ?>" min="0" required>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="manual_release_notes" class="form-label">Release Notes</label>
                                            <textarea class="form-control" id="manual_release_notes" name="release_notes" rows="3" placeholder="Describe what's new in this version..."></textarea>
                                        </div>

                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-edit"></i> Update Version
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Version Guidelines -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Versioning Guidelines</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <h6 class="text-danger">Major Version (X.0.0)</h6>
                                            <ul class="small">
                                                <li>Breaking changes</li>
                                                <li>Major system upgrades</li>
                                                <li>Incompatible API changes</li>
                                                <li>Complete feature overhauls</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-warning">Minor Version (0.X.0)</h6>
                                            <ul class="small">
                                                <li>New features</li>
                                                <li>New functionality</li>
                                                <li>Backward compatible changes</li>
                                                <li>Feature enhancements</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-4">
                                            <h6 class="text-success">Patch Version (0.0.X)</h6>
                                            <ul class="small">
                                                <li>Bug fixes</li>
                                                <li>Security patches</li>
                                                <li>Minor improvements</li>
                                                <li>Documentation updates</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
