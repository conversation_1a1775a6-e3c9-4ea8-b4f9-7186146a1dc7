<?= $this->extend('templates/employees_portal_temp') ?>

<?= $this->section('content') ?>
<div class="container py-5">
    <!-- Profile Header with Photo -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-body text-center">
                    <div class="position-relative d-inline-block mb-3">
                        <img src="<?= imgcheck($employee['id_photo']) ?>"
                            alt="Profile Photo"
                            class="rounded-circle img-thumbnail profile-photo"
                            style="width: 150px; height: 150px; object-fit: cover;">
                        <button type="button"
                            class="btn btn-sm btn-primary position-absolute bottom-0 end-0"
                            data-bs-toggle="modal"
                            data-bs-target="#updatePhotoModal">
                            <i class="fas fa-camera"></i>
                        </button>
                    </div>
                    <h4 class="mb-1"><?= esc($employee['fname']) ?> <?= esc($employee['lname']) ?></h4>
                    <p class="text-muted">File No: <?= esc($employee['fileno']) ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Information -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i>Edit Profile Information</h5>
                </div>
                <div class="card-body">
                    <?php if (session()->has('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <?= session('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->has('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <?= session('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?= form_open('employee_portal/my_profile/update', ['id' => 'profileUpdateForm']) ?>
                    <div class="small text-muted mb-3">
                        Fields marked with <span class="text-danger">*</span> are required
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Gender <span class="text-danger">*</span></label>
                            <select name="gender" class="form-select" required>
                                <option value="">-- Select Gender --</option>
                                <option value="Male" <?= $employee['gender'] === 'Male' ? 'selected' : '' ?>>Male</option>
                                <option value="Female" <?= $employee['gender'] === 'Female' ? 'selected' : '' ?>>Female</option>
                            </select>
                            <div class="invalid-feedback" id="gender-error"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Date of Birth <span class="text-danger">*</span></label>
                            <input type="date" name="dobirth" class="form-control" 
                                value="<?= ($employee['dobirth'] && $employee['dobirth'] != '0000-00-00') ? esc($employee['dobirth']) : '' ?>" 
                                required>
                            <div class="invalid-feedback" id="dobirth-error"></div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Phone Number</label>
                            <input type="tel" name="phone" class="form-control" value="<?= esc($employee['phone']) ?>">
                            <div class="invalid-feedback" id="phone-error"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Primary Email</label>
                            <input type="email" name="primary_email" class="form-control" value="<?= esc($employee['primary_email']) ?>">
                            <div class="invalid-feedback" id="email-error"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Other Contacts</label>
                        <textarea name="other_contacts" class="form-control" rows="2"><?= esc($employee['other_contacts']) ?></textarea>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">New Password</label>
                            <input type="password" name="password" class="form-control">
                            <small class="text-muted">Leave empty to keep current password</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Confirm New Password</label>
                            <input type="password" name="confirm_password" class="form-control">
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="btnUpdateProfile">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                        <a href="<?= base_url('employee_portal/dashboard') ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Photo Update Modal -->
<div class="modal fade" id="updatePhotoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-camera me-2"></i>Update Profile Photo</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open_multipart('employee_portal/my_profile/update_photo', ['id' => 'photoUpdateForm']) ?>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <?php if (!empty($employee['id_photo'])): ?>
                        <img src="<?= imgcheck($employee['id_photo']) ?>"
                            alt="Current Photo"
                            class="img-thumbnail mb-2 profile-photo"
                            style="max-width: 200px;">
                        <p class="text-muted">Current Photo</p>
                    <?php endif; ?>
                </div>
                <div class="mb-3">
                    <label class="form-label">Select New Photo</label>
                    <input type="file" name="id_photo" class="form-control" accept="image/*" required>
                    <small class="text-muted">Supported formats: JPG, PNG, GIF (Max size: 5MB)</small>
                </div>
                <!-- Error message container -->
                <div class="alert alert-danger mt-2 d-none" id="photoErrorMessage"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary" id="btnUpdatePhoto">
                    <i class="fas fa-upload me-2"></i>Update Photo
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get CSRF token
    let csrfToken = $('meta[name="<?= csrf_token() ?>"]').attr('content');
    const csrfHeader = "<?= csrf_header() ?>";
    
    // Setup AJAX CSRF headers
    $.ajaxSetup({
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            [csrfHeader]: csrfToken
        }
    });

    // Update CSRF headers function
    const updateCsrfToken = (newToken) => {
        csrfToken = newToken;
        $('meta[name="<?= csrf_token() ?>"]').attr('content', newToken);
        $.ajaxSetup({
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                [csrfHeader]: newToken
            }
        });
    };

    // Profile Update Form Submission
    $('#profileUpdateForm').on('submit', function(e) {
        e.preventDefault();
        
        // Validate date before submission
        const dateInput = $('input[name="dobirth"]');
        if (!dateInput.val()) {
            Swal.fire({
                title: 'Error!',
                text: 'Please enter a valid date of birth',
                icon: 'error',
                confirmButtonColor: '#CE1126',
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true,
                didOpen: () => {
                    document.getElementById('loading-spinner').style.display = 'none';
                }
            });
            dateInput.focus();
            return false;
        }
        
        // Add CSRF token to form data
        const formData = $(this).serialize() + '&<?= csrf_token() ?>=' + csrfToken;
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('#btnUpdateProfile').prop('disabled', true)
                    .html('<i class="fas fa-spinner fa-spin me-2"></i>Updating...');
            },
            success: function(response) {
                if (response.status === 'success') {
                    Swal.fire({
                        title: 'Success!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonColor: '#CE1126',
                        showConfirmButton: false,
                        timer: 2000,
                        timerProgressBar: true,
                        didOpen: () => {
                            document.getElementById('loading-spinner').style.display = 'none';
                        }
                    });
                    
                    // Update CSRF token
                    if (response.csrf_token) {
                        updateCsrfToken(response.csrf_token);
                    }
                } else {
                    // Reset previous error states
                    $('.form-control, .form-select').removeClass('is-invalid');
                    $('.invalid-feedback').html('');
                    
                    if (response.errors) {
                        // Display validation errors under each field
                        Object.keys(response.errors).forEach(function(field) {
                            $(`[name="${field}"]`)
                                .addClass('is-invalid')
                                .siblings('.invalid-feedback')
                                .html(response.errors[field]);
                        });
                    }
                    
                    Swal.fire({
                        title: 'Error!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonColor: '#CE1126',
                        showConfirmButton: false,
                        timer: 2000,
                        timerProgressBar: true,
                        didOpen: () => {
                            document.getElementById('loading-spinner').style.display = 'none';
                        }
                    });
                }

                // Update CSRF token if provided
                if (response.csrf_token) {
                    updateCsrfToken(response.csrf_token);
                }
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred while updating the profile';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonColor: '#CE1126',
                    showConfirmButton: false,
                    timer: 2000,
                    timerProgressBar: true,
                    didOpen: () => {
                        document.getElementById('loading-spinner').style.display = 'none';
                    }
                });
            },
            complete: function() {
                $('#btnUpdateProfile').prop('disabled', false)
                    .html('<i class="fas fa-save me-2"></i>Update Profile');
            }
        });
    });

    // Photo Update Form Submission
    $('#photoUpdateForm').on('submit', function(e) {
        e.preventDefault();
        
        // Hide any previous error messages
        $('#photoErrorMessage').addClass('d-none').html('');
        
        // Check file size before upload
        const fileInput = $('input[name="id_photo"]')[0];
        if (fileInput.files.length > 0) {
            const fileSize = fileInput.files[0].size / 1024 / 1024; // in MB
            if (fileSize > 5) {
                $('#photoErrorMessage').removeClass('d-none').html('File size must not exceed 5MB');
                return false;
            }
        }

        var formData = new FormData(this);
        formData.append('<?= csrf_token() ?>', csrfToken);
        
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            beforeSend: function() {
                $('#btnUpdatePhoto').prop('disabled', true)
                    .html('<i class="fas fa-spinner fa-spin me-2"></i>Uploading...');
            },
            success: function(response) {
                if (response.status === 'success') {
                    $('#updatePhotoModal').modal('hide');
                    
                    // Update the profile image if URL is provided
                    if (response.data && response.data.photo_url) {
                        $('.profile-photo').attr('src', response.data.photo_url);
                    }
                    
                    Swal.fire({
                        title: 'Success!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonColor: '#CE1126',
                        showConfirmButton: false,
                        timer: 2000,
                        timerProgressBar: true,
                        didOpen: () => {
                            document.getElementById('loading-spinner').style.display = 'none';
                        }
                    });
                    
                    // Update CSRF token
                    if (response.csrf_token) {
                        updateCsrfToken(response.csrf_token);
                    }
                } else {
                    $('#photoErrorMessage').removeClass('d-none').html(response.message);
                    Swal.fire({
                        title: 'Error!',
                        text: response.message,
                        icon: 'error',
                        confirmButtonColor: '#CE1126',
                        showConfirmButton: false,
                        timer: 2000,
                        timerProgressBar: true,
                        didOpen: () => {
                            document.getElementById('loading-spinner').style.display = 'none';
                        }
                    });
                }
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred while uploading the photo';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                $('#photoErrorMessage').removeClass('d-none').html(errorMessage);
                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonColor: '#CE1126',
                    showConfirmButton: false,
                    timer: 2000,
                    timerProgressBar: true,
                    didOpen: () => {
                        document.getElementById('loading-spinner').style.display = 'none';
                    }
                });
            },
            complete: function() {
                $('#btnUpdatePhoto').prop('disabled', false)
                    .html('<i class="fas fa-upload me-2"></i>Update Photo');
            }
        });
    });

    // File input change handler to validate file type
    $('input[name="id_photo"]').on('change', function() {
        const file = this.files[0];
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        
        $('#photoErrorMessage').addClass('d-none').html('');
        
        if (file) {
            if (!allowedTypes.includes(file.type)) {
                $('#photoErrorMessage').removeClass('d-none')
                    .html('Please select a valid image file (JPG, PNG, or GIF)');
                this.value = ''; // Clear the input
                return false;
            }
            
            const fileSize = file.size / 1024 / 1024; // in MB
            if (fileSize > 5) {
                $('#photoErrorMessage').removeClass('d-none')
                    .html('File size must not exceed 5MB');
                this.value = ''; // Clear the input
                return false;
            }
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>