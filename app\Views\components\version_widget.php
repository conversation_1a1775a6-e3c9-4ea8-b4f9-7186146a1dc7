<?php
// Load version helper if not already loaded
if (!function_exists('get_version_info')) {
    helper('version');
}

$versionInfo = get_version_info();
$displayStyle = $displayStyle ?? 'badge'; // badge, text, or detailed
?>

<?php if ($displayStyle === 'badge'): ?>
    <span class="badge badge-secondary">
        <i class="fas fa-code-branch"></i> v<?= esc($versionInfo['version']) ?>
    </span>

<?php elseif ($displayStyle === 'text'): ?>
    <span class="text-muted small">
        Version <?= esc($versionInfo['version']) ?>
    </span>

<?php elseif ($displayStyle === 'detailed'): ?>
    <div class="version-info">
        <h6 class="mb-2">
            <i class="fas fa-code-branch"></i> Version Information
        </h6>
        <table class="table table-sm">
            <tr>
                <td><strong>Version:</strong></td>
                <td><?= esc($versionInfo['version']) ?></td>
            </tr>
            <tr>
                <td><strong>Release Date:</strong></td>
                <td><?= esc($versionInfo['release_date']) ?></td>
            </tr>
            <tr>
                <td><strong>Build:</strong></td>
                <td>#<?= esc($versionInfo['build_number']) ?></td>
            </tr>
            <?php if (!empty($versionInfo['release_notes'])): ?>
            <tr>
                <td><strong>Notes:</strong></td>
                <td><?= esc($versionInfo['release_notes']) ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>

<?php else: ?>
    <!-- Default: simple version display -->
    <span>v<?= esc($versionInfo['version']) ?></span>
<?php endif; ?>

<!-- Usage Examples:
     
     Simple version display:
     <?= view('components/version_widget') ?>
     
     Badge style:
     <?= view('components/version_widget', ['displayStyle' => 'badge']) ?>
     
     Text style:
     <?= view('components/version_widget', ['displayStyle' => 'text']) ?>
     
     Detailed view:
     <?= view('components/version_widget', ['displayStyle' => 'detailed']) ?>
-->
