<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="Dakoii Admin" content="This is Dakoii Admin Interface" />
    <link rel="shortcut icon" href="<?= base_url() ?>public/assets/system_img/favicon.ico" type="image/x-icon">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootswatch@4.5.2/dist/solar/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">



    <!-- Toastr CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>


    <!-- PWA Headers -->
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="theme-color" content="#ffffff">
    <link rel="manifest" href="<?= base_url() ?>public/manifest.json">
    <link rel="apple-touch-icon" href="<?= base_url() ?>public/assets/icons/icon-144x144.png">

    <title><?= $title ?></title>


    <!-- Custom file input handler -->
    <script>
        $(document).ready(function() {
            // Custom file input handler to replace bsCustomFileInput
            $(document).on('change', '.custom-file-input', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName || 'Choose file');
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            <?php if (session()->getFlashdata('error')) : ?>
                toastr.error("<?= session()->getFlashdata('error') ?>");
            <?php endif ?>
            <?php if (session()->getFlashdata('success')) : ?>
                toastr.success("<?= session()->getFlashdata('success') ?>");
            <?php endif ?>
        });
    </script>

    <!-- PWA Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('<?= base_url() ?>public/service-worker.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    })
                    .catch(function(error) {
                        console.log('ServiceWorker registration failed: ', error);
                    });
            });
        }
    </script>
</head>

<body>


    <section>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="<?= base_url() ?>">
                    <img src="<?= base_url() ?>public/assets/system_img/dakoii-logo.png" alt="Brand Logo" width="30" height="30" class="d-inline-block align-text-top">
                    <?= SYSTEM_NAME ?></a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse justify-content-center" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">

                            <a class="nav-link" href="#">  </a>
                        </li>

                        <li class="nav-item">
                            <?php
                            $active = "";
                            if ($menu == "dlogin") {
                                $active = "active";
                            }
                            ?>
                            <a class="nav-link <?= $active ?>" href="<?= base_url() ?>dakoii/dashboard"> <i class="fas fa-tachometer-alt" aria-hidden="true"></i>
                                D-Dashboard </a>
                        </li>
                        <li class="nav-item">
                            <?php
                            $active = "";
                            if ($menu == "dlogin") {
                                $active = "active";
                            }
                            ?>
                            <a class="nav-link text-danger <?= $active ?>" href="<?= base_url() ?>dlogout">
                                <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                                Logout (<?= session('user_username') ?>) </a>
                        </li>

                    </ul>
                </div>
            </div>
        </nav>

    </section>

    <div class="row">
        <div class="col-md-12">
            <!-- Inside your view -->
            <?php if ($success = session()->getFlashdata('success')) : ?>
                <div class="alert alert-success"><?= $success ?></div>
            <?php endif; ?>

            <?php if ($error = session()->getFlashdata('error')) : ?>
                <div class="alert alert-danger"><?= $error ?></div>
            <?php endif; ?>

        </div>
    </div>

    <?php echo $this->renderSection('content') ?>

    <footer class="footer bg-dark">
        <div class="container">
            <div class="row pt-1">
                <div class="col-lg-12">
                    <p class="text-muted text-center">
                        &copy; <?= date('Y') ?> <a href="https://www.dakoiims.com">Dakoii Systems</a>
                        . <?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?>
                        <?php if (function_exists('get_version_info')): ?>
                            <?php $versionInfo = get_version_info(); ?>
                            <small class="ml-2">
                                (Build #<?= $versionInfo['build_number'] ?> - <?= date('M d, Y', strtotime($versionInfo['release_date'])) ?>)
                            </small>
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>
    </footer>



</body>


<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.slim.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.min.js"></script>


</html>