<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' : '' ?>GovPSS</title>
    <link rel="shortcut icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico" type="image/x-icon">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">

    <style>
        :root {
            --png-red: #CE1126;
            --png-black: #000000;
            --png-gold: #FCD116;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }

        .gradient-bg {
            background: linear-gradient(135deg, rgba(206, 17, 38, 0.9), rgba(0, 0, 0, 0.9));
        }

        .btn-primary {
            background-color: var(--png-red);
            border-color: var(--png-red);
        }

        .btn-primary:hover {
            background-color: #a00d1d;
            border-color: #a00d1d;
        }

        .text-primary {
            color: var(--png-red) !important;
        }

        .border-primary {
            border-color: var(--png-red) !important;
        }

        .form-control:focus {
            border-color: var(--png-red);
            box-shadow: 0 0 0 0.25rem rgba(206, 17, 38, 0.25);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="Logo" height="40" class="me-2">
                <span>GovPSS</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a href="<?= base_url() ?>" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('about') ?>" class="nav-link">About</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header Banner -->
    <div class="gradient-bg py-3">
        <h4 class="text-center text-white mb-0">GOVERNMENT PUBLIC SERVANTS SUPPORT SYSTEM</h4>
    </div>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h4 class="fw-bold">Employee Login</h4>
                            <p class="text-muted">Please verify your identity to continue</p>
                        </div>

                        <!-- Employee Details -->
                        <div class="mb-4 p-3 bg-light rounded">
                            <div class="row mb-2">
                                <div class="col-4 text-muted">Name:</div>
                                <div class="col-8 fw-semibold"><?= esc($employee['fname']) ?> <?= esc($employee['lname']) ?></div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-4 text-muted">File Number:</div>
                                <div class="col-8 fw-semibold"><?= esc($employee['fileno']) ?></div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-4 text-muted">Position:</div>
                                <div class="col-8 fw-semibold">
                                    <?= !empty($employee['position_name']) ? esc($employee['position_name']) : 'Not assigned' ?>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-4 text-muted">Type:</div>
                                <div class="col-8 fw-semibold">
                                    <?= !empty($employee['appointment_type']) ? esc($employee['appointment_type']) : 'Not specified' ?>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-4 text-muted">Group:</div>
                                <div class="col-8 fw-semibold">
                                    <?= !empty($employee['group_name']) ? esc($employee['group_name']) : 'Not assigned' ?>
                                </div>
                            </div>
                        </div>

                        <?php if(session()->getFlashdata('error')): ?>
                            <div class="alert alert-danger" role="alert">
                                <?= session()->getFlashdata('error') ?>
                            </div>
                        <?php endif; ?>

                        <form action="<?= base_url('employee_portal/login') ?>" method="POST" autocomplete="off">
                            <?= csrf_field() ?>
                            <input type="hidden" name="emp_id" value="<?= esc($employee['emp_id']) ?>">
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           placeholder="Enter your password"
                                           autocomplete="new-password"
                                           required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="<?= base_url() ?>" class="text-decoration-none text-muted">
                        <i class="fas fa-arrow-left me-2"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <div class="mb-3">
                <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Dakoii" height="32">
            </div>
            <p class="small mb-1">&copy; 2024 <a href="https://www.dakoiims.com" class="text-warning text-decoration-none">Dakoii Systems</a></p>
            <p class="small mb-0"><?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

    <!-- Password Toggle Script -->
    <script>
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    </script>

    <!-- SweetAlert Messages -->
    <script>
        <?php if (session()->getFlashdata('success')): ?>
            Swal.fire({
                title: 'Success!',
                text: '<?= session()->getFlashdata('success') ?>',
                icon: 'success',
                confirmButtonColor: '#CE1126'
            });
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            Swal.fire({
                title: 'Error!',
                text: '<?= session()->getFlashdata('error') ?>',
                icon: 'error',
                confirmButtonColor: '#CE1126'
            });
        <?php endif; ?>
    </script>
</body>
</html> 